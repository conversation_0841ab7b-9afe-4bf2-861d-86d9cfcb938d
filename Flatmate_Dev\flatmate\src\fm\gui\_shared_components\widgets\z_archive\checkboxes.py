"""
Checkbox Components

Provides reusable checkbox components that follow the application's design patterns.
These components can be used across different modules for consistent checkbox UI.
"""

from PySide6.QtCore import Signal, Qt
from PySide6.QtWidgets import QWidget, QCheckBox, QHBoxLayout


class LabeledCheckBox(QWidget):
    """
    A checkbox with a label in a horizontal layout.
    
    Signals:
        state_changed: Emitted when the checkbox state changes
    """
    
    state_changed = Signal(bool)
    
    def __init__(self, label_text, checked=False, tooltip=None, parent=None):
        """
        Initialize the labeled checkbox.
        
        Args:
            label_text: Text to display next to the checkbox
            checked: Initial state of the checkbox
            tooltip: Optional tooltip text
            parent: Parent widget
        """
        super().__init__(parent)
        self._init_ui(label_text, checked, tooltip)
        self._connect_signals()
    
    def _init_ui(self, label_text, checked, tooltip):
        """Initialize the UI components."""
        # Main layout
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(5)
        
        # Checkbox
        self.checkbox = QCheckBox(label_text)
        self.checkbox.setChecked(checked)
        if tooltip:
            self.checkbox.setToolTip(tooltip)
        
        layout.addWidget(self.checkbox)
        layout.addStretch()
    
    def _connect_signals(self):
        """Connect widget signals."""
        self.checkbox.stateChanged.connect(
            lambda state: self.state_changed.emit(state == Qt.CheckState.Checked)
        )
    
    def is_checked(self):
        """Get the current state of the checkbox."""
        return self.checkbox.isChecked()
    
    def set_checked(self, checked):
        """Set the state of the checkbox."""
        self.checkbox.setChecked(checked)
