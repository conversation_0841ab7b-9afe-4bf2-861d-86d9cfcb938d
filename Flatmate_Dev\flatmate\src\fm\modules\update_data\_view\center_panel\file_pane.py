"""
File pane component for the center panel.
"""

import os

from PySide6.QtCore import Signal, Qt
from PySide6.QtWidgets import QFrame, QLabel, QSizePolicy, QVBoxLayout, QSplitter, QWidget

from fm.gui._shared_components.base.base_pane import BasePane
from .widgets.file_browser import FileDisplayWidget

class FileBrowser(QFrame):
    """File browser widget for displaying and managing files."""
    
    # Signals for publishing events
    publish_file_removed = Signal(str)  # Publishes path of removed file
    publish_file_selected = Signal(str)  # Publishes path of selected file
    publish_add_files_requested = Signal()  # Publishes request to add files
    publish_files_added = Signal(list)  # Publishes list of added files
    
    def __init__(self, parent=None):
        """Initialize the file browser."""
        super().__init__(parent)
        self._init_ui()
        self._connect_signals()
    
    def _init_ui(self):
        """Initialize the UI components."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # Use the FileDisplayWidget for showing files
        self.file_display = FileDisplayWidget(self)
        layout.addWidget(self.file_display)
    
    def _connect_signals(self):
        """Connect internal signals."""
        # Forward signals from file display to subscribers
        self.file_display.file_removed.connect(self.publish_file_removed)
        self.file_display.file_selected.connect(self.publish_file_selected)
        self.file_display.add_files_requested.connect(self.publish_add_files_requested)
    
    def set_source_path(self, path: str):
        """Set the source folder path."""
        # No UI update needed here, just for API completeness
        pass
    
    def set_save_path(self, path: str):
        """Set the save location path."""
        self.save_path.setText(path)
    
    def set_files(self, files: list, source_dir: str = ""):
        """Set the files to display in the tree.
        
        Args:
            files: List of file paths or filenames
            source_dir: Source directory for relative paths
        """
        # Ensure we have valid files to display
        if not files:
            # Clear the display if no files
            self.file_display.set_files([], "")
            return
            
        # Make sure we're working with full paths
        full_paths = []
        for file in files:
            if source_dir and not os.path.isabs(file):
                full_paths.append(os.path.join(source_dir, file))
            else:
                full_paths.append(file)
        
        # Pass the full paths to the file display widget
        self.file_display.set_files(full_paths, source_dir)
    
    def get_files(self) -> list[str]:
        """Get all file paths in the tree."""
        # Get files from the file display widget
        return self.file_display.get_files() if hasattr(self.file_display, 'get_files') else []


class FilePane(BasePane):
    """Pane component for displaying and managing files."""
    
    # Signals for publishing events to subscribers
    publish_file_removed = Signal(str)  # Publishes path of removed file
    publish_file_selected = Signal(str)  # Publishes path of selected file
    publish_add_files_requested = Signal()  # Publishes request to add files
    publish_files_added = Signal(list)  # Publishes list of added files
    
    def __init__(self, parent=None):
        """Initialize the file pane."""
        super().__init__(parent)
        self._init_ui()
        self._connect_signals()
    
    def _init_ui(self):
        """Initialize the UI components."""
        # Set this widget to expand to fill available space
        self.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        
        # Main layout with no margins
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(10)
        
        # Info section - fixed height
        info_widget = QWidget()
        info_layout = QVBoxLayout(info_widget)
        info_layout.setContentsMargins(10, 10, 10, 10)
        
        # Source folder
        self.source_label = QLabel("Source Folder:")
        self.source_label.setObjectName("subheading")
        self.source_path = QLabel()
        info_layout.addWidget(self.source_label)
        info_layout.addWidget(self.source_path)
        
        # Save location
        self.save_label = QLabel("Save Location:")
        self.save_label.setObjectName("subheading")
        self.save_path = QLabel()
        info_layout.addWidget(self.save_label)
        info_layout.addWidget(self.save_path)
        
        # Add info widget to main layout
        layout.addWidget(info_widget)
        
        # Separator
        line = QFrame()
        line.setFrameShape(QFrame.Shape.HLine)
        line.setFrameShadow(QFrame.Shadow.Sunken)
        layout.addWidget(line)
        
        # Files section heading
        self.files_label = QLabel("Selected Files:")
        self.files_label.setObjectName("subheading")
        layout.addWidget(self.files_label)
        
        # File browser - takes up all available space
        self.file_browser = FileBrowser()
        self.file_browser.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        layout.addWidget(self.file_browser, 1)  # The 1 is the stretch factor
    
    def _connect_signals(self):
        """Connect internal signals."""
        # Subscribe to file browser signals
        self.file_browser.publish_file_removed.connect(self.publish_file_removed)
        self.file_browser.publish_file_selected.connect(self.publish_file_selected)
        self.file_browser.publish_add_files_requested.connect(self.publish_add_files_requested)
        self.file_browser.publish_files_added.connect(self.publish_files_added)
    
    def show_component(self):
        """Show this component."""
        self.show()
    
    def hide_component(self):
        """Hide this component."""
        self.hide()
    
    def set_source_path(self, path: str):
        """Set the source folder path."""
        self.source_path.setText(path)
    
    def set_save_path(self, path: str):
        """Set the save location path."""
        self.save_path.setText(path)
    
    def set_files(self, files: list, source_dir: str = ""):
        """Set the files to display in the tree.
        
        Args:
            files: List of file paths
            source_dir: Source directory for relative paths
        """
        self.file_browser.set_files(files, source_dir)
    
    def get_files(self) -> list[str]:
        """Get all file paths in the tree."""
        return self.file_browser.get_files()
    
    def display_welcome(self):
        """Display welcome message."""
        self.source_path.setText("")
        self.save_path.setText("")
        # Reset the file browser by clearing files
        if hasattr(self.file_browser, 'file_display'):
            self.file_browser.set_files([], "")
        
        # Update the files label with welcome text
        self.files_label.setText("Welcome to Update Data")
        self.files_label.setStyleSheet("color: #2c3e50; font-weight: bold;")
    
    def show_error(self, message: str):
        """Show error message."""
        self.files_label.setText(f"Error: {message}")
        self.files_label.setStyleSheet("color: red;")
    
    def show_success(self, message: str):
        """Show success message."""
        self.files_label.setText(message)
        self.files_label.setStyleSheet("color: green;")
