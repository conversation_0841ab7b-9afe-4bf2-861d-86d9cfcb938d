# Camelot PDF Parser Test Report

## Test Information
- **Test Date:** 2025-07-21
- **Test Time:** 12:29:08 NZST
- **Package:** Camelot-py 0.10.1
- **Extraction Method:** Stream
- **File Tested:** `tests/test_pdfs/kiwibank/2024-Dec-23_Personal.pdf`

## Test Configuration
```python
tables = camelot.read_pdf(
    'tests/test_pdfs/kiwibank/2024-Dec-23_Personal.pdf',
    flavor='stream',
    pages='all'
)
```

## Test Results
- **Tables Found:** 10
- **Pages Processed:** All pages in the PDF
- **Output Format:** CSV and JSON for each table
- **Parsing Reports:** JSON report file for each table with quality metrics

## Table Quality Summary
| Table | Accuracy | Whitespace | Page | Notes |
|-------|----------|------------|------|-------|
| 1     | 97.7%    | 16.67%     | 1    | Account balances table |
| 2-10  | Varies   | Varies     | Various | Other detected tables |

## Files Generated
- `camelot_parser_output_table_1.csv` - CSV data for table 1
- `camelot_parser_output_table_1.json` - JSON data with metadata for table 1
- `camelot_parser_output_table_1_report.json` - Parsing quality report for table 1
- (Similar files for tables 2-10)

## Notes
- Camelot's stream method successfully detected multiple tables in the PDF
- The first table (account balances) has high accuracy (97.7%)
- Some tables may be false positives or partial tables
- The stream method works well for this PDF format, while lattice method detected no tables

## Next Steps
- Review the extracted tables to identify which ones contain useful data
- Compare with other parsing methods
- Develop post-processing to clean and normalize the extracted data
