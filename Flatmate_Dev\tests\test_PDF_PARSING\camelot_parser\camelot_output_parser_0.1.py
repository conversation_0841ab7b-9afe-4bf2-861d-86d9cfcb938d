import pandas as pd
import argparse
import os
import re
import camelot

def to_numeric(series):
    """Converts a pandas Series to numeric, ignoring errors and handling currency."""
    series = series.astype(str).str.replace(r'[$,]', '', regex=True).str.strip()
    return pd.to_numeric(series, errors='coerce')

def extract_and_clean_pdf(pdf_path, output_dir):
    """Extracts tables from a PDF using Camelot, cleans them, and saves as CSVs."""
    print(f"Extracting tables from {pdf_path} using Camelot...")
    try:
        tables = camelot.read_pdf(pdf_path, flavor='stream', pages='all')
        print(f"Found {len(tables)} tables using Camelot stream")
    except Exception as e:
        print(f"Error reading PDF with Camelot: {e}")
        return

    # Ensure output directory exists
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    for i, table in enumerate(tables):
        table_num = i + 1
        df = table.df

        # Use the first row as the header and drop it
        df.columns = df.iloc[0]
        df = df.iloc[1:].reset_index(drop=True)

        # Convert money columns to numeric types for reliable checking
        # This assumes standard column names from the statement
        if 'Withdrawals' in df.columns and 'Deposits' in df.columns and 'Balance' in df.columns:
            df['Withdrawals'] = to_numeric(df['Withdrawals'])
            df['Deposits'] = to_numeric(df['Deposits'])
            df['Balance'] = to_numeric(df['Balance'])
        else:
            print(f"Skipping table {table_num}: missing required financial columns.")
            continue

        cleaned_transactions = []
        current_master_transaction = None

        for _, row in df.iterrows():
            is_master = pd.notna(row['Withdrawals']) or pd.notna(row['Deposits'])

            if is_master:
                if current_master_transaction is not None:
                    cleaned_transactions.append(current_master_transaction)
                current_master_transaction = row.to_dict()
            else:
                if current_master_transaction is not None:
                    continuation_desc = row.iloc[1]
                    if pd.notna(continuation_desc):
                        current_master_transaction['Transaction'] += f" {continuation_desc.strip()}"
        
        if current_master_transaction is not None:
            cleaned_transactions.append(current_master_transaction)

        if not cleaned_transactions:
            print(f"No transactions found to process in table {table_num}.")
            continue

        cleaned_df = pd.DataFrame(cleaned_transactions)
        output_path = os.path.join(output_dir, f"cleaned_table_{table_num}.csv")
        cleaned_df.to_csv(output_path, index=False)
        print(f"Successfully parsed and saved cleaned data to {output_path}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description='Extract and clean tables from a PDF file using Camelot.'
    )
    parser.add_argument(
        'pdf_file',
        type=str,
        help='The full path to the input PDF file.'
    )
    parser.add_argument(
        'output_dir',
        type=str,
        help='The directory to save the cleaned output CSV files.'
    )

    args = parser.parse_args()
    extract_and_clean_pdf(args.pdf_file, args.output_dir)
