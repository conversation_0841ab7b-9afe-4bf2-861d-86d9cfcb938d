"""
Base component interface for panel components across all modules.

This provides a common interface for panel components that can be used
in different modules while maintaining consistency with the GUI framework.
"""

from PySide6.QtWidgets import QWidget


class BasePanelComponent(QWidget):
    """Base component for all panel UI elements.

    This base class defines the interface for all panel components
    that need to be managed by coordinators and integrated with the main window.
    
    Used by:
    - Center panel components
    - Left panel components  
    - Right panel components
    - Any module-specific panel components
    """
    
    def __init__(self, parent=None):
        """Initialize the base panel component."""
        super().__init__(parent)
    
    def show_component(self):
        """Show this component.

        Subclasses should override this to implement component-specific show behavior.
        """
        self.show()

    def hide_component(self):
        """Hide this component.

        Subclasses should override this to implement component-specific hide behavior.
        """
        self.hide()
    
    def is_visible(self) -> bool:
        """Check if the component is visible."""
        return self.isVisible()
    
    def set_enabled(self, enabled: bool):
        """Enable or disable the component."""
        self.setEnabled(enabled)
    
    # Common methods that specific implementations may override
    def show_error(self, message: str):
        """Show error message.
        
        Args:
            message: Error message to display
        """
        pass
    
    def show_success(self, message: str):
        """Show success message.
        
        Args:
            message: Success message to display
        """
        pass
    
    def clear(self):
        """Clear component data."""
        pass
    
    def set_source_path(self, path: str):
        """Set source path information.
        
        Args:
            path: Source path for the component
        """
        pass
    
    def set_save_path(self, path: str):
        """Set save path information.
        
        Args:
            path: Save path for the component
        """
        pass
    
    def set_mode(self, mode: str):
        """Set component mode.
        
        Args:
            mode: Operating mode for the component
        """
        pass
    
    def get_files(self) -> list[str]:
        """Get files from this component if applicable.
        
        Returns:
            List of file paths managed by this component
        """
        return []
    
    def set_files(self, files: list, source_dir: str = ""):
        """Set files to be displayed in this component.
        
        Args:
            files: List of file paths
            source_dir: Source directory for relative paths
        """
        pass
    
    def display_dataframe(self, df, file_info: str = ""):
        """Display a DataFrame in this component if applicable.
        
        Args:
            df: DataFrame to display
            file_info: Information about the file being displayed
        """
        pass
