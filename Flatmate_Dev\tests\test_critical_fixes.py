#!/usr/bin/env python3
"""
Test script to verify the critical fixes for auto-import functionality.
"""

import os
import sys
from pathlib import Path

# Add the flatmate source directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'flatmate', 'src'))

def test_dialog_state_management():
    """Test that dialog browse buttons are enabled even when auto-import is disabled"""
    print("Testing dialog state management...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from fm.gui.dialogs.auto_import_config_dialog import AutoImportConfigDialog
        
        app = QApplication([])
        
        # Create dialog
        dialog = AutoImportConfigDialog()
        
        # Check initial state - auto-import should be disabled by default
        print(f"Auto-import enabled by default: {dialog.enabled_checkbox.isChecked()}")
        
        # Check that config group is enabled (this was the bug)
        print(f"Config group enabled: {dialog.config_group.isEnabled()}")
        print(f"Import browse button enabled: {dialog.import_browse_btn.isEnabled()}")
        print(f"Archive browse button enabled: {dialog.archive_browse_btn.isEnabled()}")
        print(f"Failed browse button enabled: {dialog.failed_browse_btn.isEnabled()}")
        
        # The fix should make these all True even when auto-import is disabled
        if (dialog.config_group.isEnabled() and 
            dialog.import_browse_btn.isEnabled() and 
            dialog.archive_browse_btn.isEnabled() and 
            dialog.failed_browse_btn.isEnabled()):
            print("✅ PASS: Browse buttons are enabled when auto-import is disabled")
            return True
        else:
            print("❌ FAIL: Browse buttons are still disabled")
            return False
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_source_options_enum():
    """Test that the source options enum includes auto-import"""
    print("Testing source options enum...")
    
    try:
        from fm.modules.update_data.utils.option_types import SourceOptions
        
        # Check that auto-import option exists
        print(f"SET_AUTO_IMPORT exists: {hasattr(SourceOptions, 'SET_AUTO_IMPORT')}")
        
        if hasattr(SourceOptions, 'SET_AUTO_IMPORT'):
            print(f"SET_AUTO_IMPORT value: '{SourceOptions.SET_AUTO_IMPORT.value}'")
            
            # Check all options
            all_options = [opt.value for opt in SourceOptions]
            print(f"All source options: {all_options}")
            
            expected_options = [
                "Select entire folder...",
                "Select individual files...",
                "Set auto import folder..."
            ]
            
            if set(expected_options) == set(all_options):
                print("✅ PASS: All expected source options present")
                return True
            else:
                print("❌ FAIL: Source options don't match expected")
                return False
        else:
            print("❌ FAIL: SET_AUTO_IMPORT option missing")
            return False
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_keys():
    """Test that configuration keys exist"""
    print("Testing configuration keys...")
    
    try:
        from fm.core.config.keys import ConfigKeys
        
        # Check AutoImport keys exist
        print(f"AutoImport keys exist: {hasattr(ConfigKeys, 'AutoImport')}")
        
        if hasattr(ConfigKeys, 'AutoImport'):
            auto_import_keys = ConfigKeys.AutoImport
            print(f"ENABLED exists: {hasattr(auto_import_keys, 'ENABLED')}")
            print(f"IMPORT_PATH exists: {hasattr(auto_import_keys, 'IMPORT_PATH')}")
            print(f"ARCHIVE_PATH exists: {hasattr(auto_import_keys, 'ARCHIVE_PATH')}")
            print(f"FAILED_PATH exists: {hasattr(auto_import_keys, 'FAILED_PATH')}")
            
            required_keys = ['ENABLED', 'IMPORT_PATH', 'ARCHIVE_PATH', 'FAILED_PATH']
            all_exist = all(hasattr(auto_import_keys, key) for key in required_keys)
            
            if all_exist:
                print("✅ PASS: All required config keys exist")
                return True
            else:
                print("❌ FAIL: Some config keys missing")
                return False
        else:
            print("❌ FAIL: AutoImport config keys missing")
            return False
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_folder_creation():
    """Test that default folder creation works"""
    print("Testing folder creation logic...")
    
    try:
        from pathlib import Path
        
        # Test the folder creation logic (without actually creating folders)
        downloads_path = Path.home() / "Downloads"
        default_import_path = downloads_path / "flatmate_imports"
        
        print(f"Downloads path: {downloads_path}")
        print(f"Default import path: {default_import_path}")
        print(f"Archive path: {default_import_path / 'archive'}")
        print(f"Failed path: {default_import_path / 'failed_imports'}")
        
        # Check if Downloads folder exists (it should on most systems)
        if downloads_path.exists():
            print("✅ PASS: Downloads folder exists, folder creation should work")
            return True
        else:
            print("⚠️  WARNING: Downloads folder doesn't exist, may need different default")
            return True  # Still pass, just a warning
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    print("=== Critical Fixes Verification ===")
    print()
    
    tests = [
        ("Source Options Enum", test_source_options_enum),
        ("Configuration Keys", test_config_keys),
        ("Folder Creation Logic", test_folder_creation),
        ("Dialog State Management", test_dialog_state_management),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"Running: {test_name}")
        result = test_func()
        results.append((test_name, result))
        print()
    
    print("=== Test Results ===")
    passed = 0
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nPassed: {passed}/{len(tests)}")
    
    if passed == len(tests):
        print("🎉 All critical fixes verified!")
    else:
        print("⚠️  Some issues remain - check failed tests above")
