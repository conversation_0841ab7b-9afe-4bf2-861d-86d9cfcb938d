#!/usr/bin/env python3
"""
Dialog Utilities

Provides convenient functions for showing common dialogs throughout the application.
"""

from typing import Optional
from PySide6.QtWidgets import QWidget

from fm.gui.dialogs import AutoImportConfigDialog
from fm.core.services.logger import log


def show_auto_import_config(parent: Optional[QWidget] = None) -> bool:
    """
    Show the auto-import configuration dialog.
    
    Args:
        parent: Parent widget for the dialog
        
    Returns:
        True if user clicked OK and saved settings, False if cancelled
    """
    try:
        dialog = AutoImportConfigDialog(parent)
        result = dialog.exec()
        
        if result == AutoImportConfigDialog.DialogCode.Accepted:
            log.info("Auto-import configuration dialog completed successfully")
            return True
        else:
            log.info("Auto-import configuration dialog cancelled")
            return False
            
    except Exception as e:
        log.error(f"Error showing auto-import configuration dialog: {e}")
        return False


# For convenience, also provide a function that can be called from anywhere
def configure_auto_import(parent: Optional[QWidget] = None) -> bool:
    """
    Convenience function to configure auto-import settings.
    
    This is the main entry point for auto-import configuration.
    Can be called from menus, buttons, or other UI elements.
    
    Args:
        parent: Parent widget for the dialog
        
    Returns:
        True if configuration was saved, False if cancelled
    """
    return show_auto_import_config(parent)
