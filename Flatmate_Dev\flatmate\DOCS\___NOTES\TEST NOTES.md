
piss the old info bar off 

test statement csv is good .. are we also polling on app start ?
save location behaves oddly 
when the dialog opens and I navigate to the folder I would like ot be my archive it has "last_used" pre populated in the text box this use dot work fine I suspect the logic has been corrupted at some point>>
- quesiton is it possible to actually dynamically change the content of the ocmbo box
not just add an entry thats there all the time 
So they can double as status signals not just options
The configure button text  shows but does nothig at this point 
update database still has a budeget chb still has a werid black background.. telling me either its configured wrong or not using a base eidget from gui shared 

-options disapear when database cb is deselected and do not re apear whe re checked 

is also supposed ot be a create master or update master depending on the master_file_tracker(if last_saved exists default to last saved)



save location is now setable -  ackmowledged in info bar but no where else (not in combo box or centeal panel )

on changing data base setting file tree disapeared entirely .. didnt come back 

_When trie dto set slelct entire folder to import folder (autooimport folder in downloads got infinite recursion

```bash

)[fm.modules.update_data.utils.statement_handlers.asb_standard_csv_handler] [WARNING] Could not extract full ASB account number from metadata line: 2025-01-15
Traceback (most recent call last):
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\update_data\ud_presenter.py", line 274, in _handle_source_select
    self.view.display_selected_source(self.selected_source)      
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^      
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\update_data\_view\ud_view.py", line 134, in display_selected_source
    self.center_display.set_files(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        source_info["file_paths"], source_info["path"]
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\update_data\_view\center_panel\_panel_manager.py", line 98, in set_files
    self.file_pane.set_files(files, source_dir)
    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\update_data\_view\center_panel\file_pane.py", line 178, in set_files
    self.file_browser.set_files(files, source_dir)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\update_data\_view\center_panel\file_pane.py", line 76, in set_files
    self.file_display.set_files(full_paths, source_dir)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\update_data\_view\center_panel\widgets\file_browser.py", line 204, in set_files
    self._add_file_item(file_path, file_info, source_dir)        
    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^        
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\update_data\_view\center_panel\widgets\file_browser.py", line 220, in _add_file_item
    parent_item = self._ensure_folder_path(parent_dir, source_dir)
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\update_data\_view\center_panel\widgets\file_browser.py", line 257, in _ensure_folder_path
    parent_item = self._ensure_folder_path(parent_dir, source_dir)
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\update_data\_view\center_panel\widgets\file_browser.py", line 257, in _ensure_folder_path
    parent_item = self._ensure_folder_path(parent_dir, source_dir)
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\update_data\_view\center_panel\widgets\file_browser.py", line 257, in _ensure_folder_path
    parent_item = self._ensure_folder_path(parent_dir, source_dir)
  [Previous line repeated 982 more times]
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\update_data\_view\center_panel\widgets\file_browser.py", line 256, in _ensure_folder_path
    parent_dir = os.path.dirname(folder_path)
  File "<frozen ntpath>", line 263, in dirname
  File "<frozen ntpath>", line 228, in split
RecursionError: maximum recursion depth exceeded
```


what iis ui_applier, that should be the context manager should it not ?

- shifted ui applier logic to view context manager. check file size /complexity