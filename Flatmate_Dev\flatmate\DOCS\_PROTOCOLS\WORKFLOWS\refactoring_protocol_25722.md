# Refactoring Protocol

## 1. Pre-Refactor Documentation
- **Architecture Overview**: High-level diagrams and markdown (`ARCHITECTURE.md`) describing current structure, dependencies, and module responsibilities.
- **Component/Module READMEs**: Each major folder (e.g., `widgets/`, `services/`) has a concise `README.md` summarising purpose, key classes, and usage.
- **Design Rationale**: A `docs/design_decisions.md` file records architectural choices, trade-offs, and reasons for the current design.

## 2. Refactor Plan & Protocol
- **Refactor Plan Document**: Create `docs/refactor_plan.md` outlining:
  - Motivation and goals
  - Scope (what will/won’t change)
  - Step-by-step migration plan
  - Risks and mitigation
  - Rollback strategy
- **Migration Checklist**: Track progress and blockers in a checklist format.
- **AI-Optimised Protocol**: Use explicit, structured markdown sections, clear naming, and bullet points for easy parsing by AI tools.

## 3. During Refactor
- **Changelog**: Maintain a running `CHANGELOG.md` with dated, atomic entries for each significant change.
- **Inline Docstrings**: Update class/function/module docstrings to reflect new responsibilities and usage.
- **Deprecation Notices**: Mark deprecated files/classes with clear comments and references to new locations.

## 4. Post-Refactor
- **Update All READMEs**: Ensure all module/component `README.md` files are current.
- **Migration Guide**: Provide a `docs/migration_guide.md` for downstream users, with before/after usage examples.
- **AI-Ready Index**: Optionally, add a `docs/code_index.md` or similar with a machine-readable (table or YAML) map of modules, their purposes, and key entry points for rapid AI onboarding.

---

**Summary:**
- Use modular, markdown-based docs at every level.
- Keep documentation concise, explicit, and structured for both humans and AI.
- Maintain up-to-date READMEs, changelogs, and migration guides.
- Record rationale and decisions in a dedicated location.

*This approach ensures clarity, traceability, and rapid AI comprehension.*
