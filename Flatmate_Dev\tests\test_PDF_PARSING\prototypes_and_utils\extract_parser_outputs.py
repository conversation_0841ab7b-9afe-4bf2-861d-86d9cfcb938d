#!/usr/bin/env python3
"""
Script to extract tables from PDF using different parsers and save as CSV and JSON.
This script extracts tables from a PDF using Camelot and pdfplumber,
and saves each table as both CSV and JSON.
"""

import os
import sys
import json
import pandas as pd
import camelot
import pdfplumber

def ensure_dir(directory):
    """Ensure directory exists."""
    if not os.path.exists(directory):
        os.makedirs(directory)

def save_camelot_tables(pdf_path, output_dir):
    """Extract tables using Camelot and save as CSV and JSON."""
    print(f"Extracting tables from {pdf_path} using Camelot...")
    
    # Extract tables using Camelot stream (since we know it works best for this PDF)
    tables = camelot.read_pdf(pdf_path, flavor='stream', pages='all')
    print(f"Found {len(tables)} tables using Camelot stream")
    
    # Save each table as CSV and JSON
    for i, table in enumerate(tables):
        table_num = i + 1
        
        # Get DataFrame
        df = table.df
        
        # Save as CSV
        csv_path = os.path.join(output_dir, f"camelot_parser_output_table_{table_num}.csv")
        df.to_csv(csv_path, index=False)
        print(f"Saved table {table_num} as CSV: {csv_path}")
        
        # Save as JSON
        json_path = os.path.join(output_dir, f"camelot_parser_output_table_{table_num}.json")
        
        # Convert DataFrame to JSON
        json_data = {
            'table_data': json.loads(df.to_json(orient='records')),
            'metadata': {
                'accuracy': table.parsing_report.get('accuracy', 'N/A'),
                'whitespace': table.parsing_report.get('whitespace', 'N/A'),
                'order': table.parsing_report.get('order', 'N/A'),
                'dimensions': f"{df.shape[0]}x{df.shape[1]}",
                'flavor': 'stream'
            }
        }
        
        with open(json_path, 'w') as f:
            json.dump(json_data, f, indent=2)
        print(f"Saved table {table_num} as JSON: {json_path}")
        
        # Save parsing report
        report_path = os.path.join(output_dir, f"camelot_parser_output_table_{table_num}_report.json")
        with open(report_path, 'w') as f:
            json.dump(table.parsing_report, f, indent=2)
        print(f"Saved table {table_num} parsing report: {report_path}")

def save_pdfplumber_tables(pdf_path, output_dir):
    """Extract tables using pdfplumber and save as CSV and JSON."""
    print(f"Extracting tables from {pdf_path} using pdfplumber...")
    
    with pdfplumber.open(pdf_path) as pdf:
        table_count = 0
        
        # Process each page
        for i, page in enumerate(pdf.pages):
            page_num = i + 1
            tables = page.extract_tables()
            
            if not tables:
                print(f"No tables found on page {page_num}")
                continue
                
            print(f"Found {len(tables)} tables on page {page_num}")
            
            # Save each table as CSV and JSON
            for j, table_data in enumerate(tables):
                table_count += 1
                
                # Convert to DataFrame
                df = pd.DataFrame(table_data)
                
                # Use first row as header if it looks like a header
                if not df.empty:
                    df.columns = df.iloc[0]
                    df = df[1:]
                
                # Save as CSV
                csv_path = os.path.join(output_dir, f"pdfplumber_parser_output_table_{table_count}.csv")
                df.to_csv(csv_path, index=False)
                print(f"Saved table {table_count} as CSV: {csv_path}")
                
                # Save as JSON
                json_path = os.path.join(output_dir, f"pdfplumber_parser_output_table_{table_count}.json")
                
                # Convert DataFrame to JSON
                json_data = {
                    'table_data': json.loads(df.to_json(orient='records')),
                    'metadata': {
                        'page': page_num,
                        'dimensions': f"{df.shape[0]}x{df.shape[1]}",
                        'raw_data': table_data  # Include the raw list of lists
                    }
                }
                
                with open(json_path, 'w') as f:
                    json.dump(json_data, f, indent=2)
                print(f"Saved table {table_count} as JSON: {json_path}")
        
        if table_count == 0:
            print("No tables found in the PDF using pdfplumber")

def main():
    """Main function."""
    if len(sys.argv) < 2:
        print("Usage: python extract_parser_outputs.py <pdf_path>")
        sys.exit(1)
    
    pdf_path = sys.argv[1]
    output_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 
                             "test_parser_outputs")
    
    ensure_dir(output_dir)
    
    # Extract and save tables using Camelot
    save_camelot_tables(pdf_path, output_dir)
    
    # Extract and save tables using pdfplumber
    save_pdfplumber_tables(pdf_path, output_dir)
    
    print("\nAll parser outputs saved to:", output_dir)

if __name__ == "__main__":
    main()
