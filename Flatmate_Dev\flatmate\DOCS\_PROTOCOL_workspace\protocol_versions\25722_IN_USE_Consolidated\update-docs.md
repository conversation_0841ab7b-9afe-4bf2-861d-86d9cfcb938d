---
type: "agent_requested"
description: "Example description"
---
# TODO: Refactor to match ideal structure
# Post-Refactoring Documentation WORKFLOW Protocol

## Documentation Structure Overview

**Main Documentation Folders:**
- `flatmate/DOCS/_ARCHITECTURE/` - **Reference Library** (stable, authoritative docs)
- `flatmate/DOCS/_FEATURES/` - **Working Folder** (active development, feature-specific)
- `flatmate/DOCS/_REPORTS/` - **Analysis and Reports**
- `flatmate/DOCS/__CHANGELOGS/` - **Change tracking**

**Feature Working Folder Structure:**
```
_FEATURES/<feature_name>/
├── _REQUIREMENTS_prd.md     # Product requirements
├── DESIGN.md               # Technical design
├── TASKS.md               # Implementation tasks
├── IMPLEMENTATION_GUIDE.md # Technical guide
├── _DISCUSSION.md         # Decisions and progress
├── CHANGELOG.md           # Session-by-session changes
└── <phase_n>_review.md    # Phase reviews
```

---

## Bulletproof Documentation Workflow (AI-Optimized)

### MANDATORY: After Every Refactoring Session

#### Step 1: **Create/Update Feature Changelog** (REQUIRED)
**Location**: `flatmate/DOCS/_FEATURES/<feature_name>/CHANGELOG.md`

**AI Actions:**
1. Create feature folder if it doesn't exist
2. Create/update CHANGELOG.md with session details:
   - **Date and Status**
   - **Summary** (what was accomplished)
   - **Changes Made** (specific files, methods, structure changes)
   - **Files Modified** (complete list with descriptions)
   - **Testing Results** (what was verified)
   - **Architecture Benefits** (why changes improve the system)
   - **Known Issues Resolved** (problems fixed)
   - **Future Enhancements** (opportunities identified)

**Template:**
```markdown
# <Feature Name> - Changelog

**Date**: YYYY-MM-DD
**Status**: [IN_PROGRESS|COMPLETED|ON_HOLD]
**Session**: [Brief session description]

## Summary
[What was accomplished this session]

## Changes Made
[Detailed list of changes]

## Files Modified
[Complete list with descriptions]

## Testing Results
[What was verified to work]

## Architecture Benefits
[Why these changes improve the system]

## Known Issues Resolved
[Problems that were fixed]

## Future Enhancements
[Opportunities for future work]
```

#### Step 2: **Update Architecture Documentation** (IF SIGNIFICANT)
**Location**: `flatmate/DOCS/_ARCHITECTURE/`

**AI Actions:**
1. If refactoring affects core architecture, update relevant architecture docs
2. Add new patterns or components to architecture reference
3. Update component relationship diagrams if needed
4. Mark obsolete patterns as deprecated

#### Step 3: **Record Technical Debt** (IF IDENTIFIED)
**Location**: `flatmate/DOCS/_ARCHITECTURE/TECHNICAL_DEBT.md`

**AI Actions:**
1. Document any architectural inconsistencies discovered
2. Note future refactoring opportunities
3. Record workarounds or temporary solutions
4. Estimate effort for proper resolution

#### Step 4: **Update Feature Status** (IF APPLICABLE)
**Location**: `flatmate/DOCS/_FEATURES/<feature_name>/_DISCUSSION.md`

**AI Actions:**
1. Update feature implementation status
2. Record any scope changes or new requirements
3. Note blockers or dependencies discovered
4. Update timeline estimates if needed

---

## Quality Gates (AI Verification)

### Before Completing Any Refactoring Session:
- [ ] **CHANGELOG.md created/updated** with complete session details
- [ ] **All modified files documented** with reasons for changes
- [ ] **Testing results recorded** (what works, what was verified)
- [ ] **Architecture impact assessed** (benefits, trade-offs, debt)
- [ ] **Future work identified** (enhancements, technical debt)

### Before Marking Feature Complete:
- [ ] **All feature documents present** (requirements, design, tasks, guide, discussion)
- [ ] **Architecture docs updated** if core patterns changed
- [ ] **Technical debt documented** if any shortcuts taken
- [ ] **User acceptance criteria met** and documented

---

## Integration with Feature Protocol

**Connection Point**: This workflow is triggered at **Step 6** of `feature-protocol_v1.1.md`

**After Implementation & Testing:**
1. Run this documentation workflow
2. Create/update CHANGELOG.md
3. Update architecture docs if needed
4. Record technical debt
5. Prepare for user review

---

## Documentation Maintenance Rules

### For AI Systems:
1. **ALWAYS create CHANGELOG.md** after refactoring sessions
2. **NEVER skip documentation** - it costs more to recreate later
3. **BE SPECIFIC** - include file paths, method names, line numbers
4. **RECORD RATIONALE** - why decisions were made, alternatives considered
5. **DOCUMENT DEBT** - architectural issues, workarounds, future work

### For Human Developers:
1. **Review changelogs** before working on related features
2. **Update architecture docs** when patterns change significantly
3. **Address technical debt** during maintenance windows
4. **Keep working folders clean** - archive completed features

---

## Success Metrics

### Documentation Quality:
- [ ] Any developer can understand what changed and why
- [ ] Architecture decisions are clearly recorded
- [ ] Technical debt is tracked and prioritized
- [ ] Feature status is always current

### Workflow Efficiency:
- [ ] Documentation takes <10% of development time
- [ ] No information is lost between sessions
- [ ] Context switching is minimized
- [ ] Future work is clearly identified

---

**This workflow ensures no work is lost and all architectural decisions are preserved for future reference.**
