#!/usr/bin/env python3
"""
Extract and display tables from Kiwibank PDF statement using Camelot stream method
"""

import os
import sys
import camelot
import pandas as pd
from pathlib import Path

def extract_tables(pdf_path):
    """Extract tables from PDF using Camelot stream method"""
    print(f"Extracting tables from: {pdf_path}")
    
    # Extract tables using Camelot stream method
    tables = camelot.read_pdf(pdf_path, flavor='stream')
    print(f"Found {len(tables)} tables")
    
    # Create output directory if it doesn't exist
    output_dir = Path("tests/test_PDF_PARSING/test_pdf_parser_output/tables")
    output_dir.mkdir(exist_ok=True)
    
    # Save each table to CSV and display info
    for i, table in enumerate(tables):
        # Display table info
        print(f"\nTable {i+1}:")
        print(f"  Dimensions: {table.df.shape}")
        print(f"  Accuracy: {table.parsing_report['accuracy']}")
        print(f"  Whitespace: {table.parsing_report['whitespace']}")
        
        # Display table content
        print("\nTable Content:")
        print(table.df)
        
        # Save to CSV
        csv_path = output_dir / f"table_{i+1}.csv"
        table.to_csv(str(csv_path))
        print(f"  Saved to: {csv_path}")

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python extract_tables.py <pdf_path>")
        sys.exit(1)
    
    pdf_path = sys.argv[1]
    if not os.path.exists(pdf_path):
        print(f"Error: PDF file not found: {pdf_path}")
        sys.exit(1)
    
    extract_tables(pdf_path)
