{"table_data": [{"0": "...W", "1": "", "2": ""}, {"0": "Account Name:", "1": "Q M SHAW-WILLIAMS", "2": ""}, {"0": "Product Name:", "1": "Back-Up Saver Account", "2": ""}, {"0": "Personalised Name:", "1": "Saver", "2": ""}, {"0": "Account Number:", "1": "38-9004-0646977-02", "2": ""}, {"0": "Statement Period:", "1": "23 November 2024 to 22 December 2024", "2": ""}, {"0": ".", "1": "", "2": ""}, {"0": "Date\nTransaction", "1": "<PERSON><PERSON><PERSON><PERSON>\nDe<PERSON>", "2": "Balance"}, {"0": "23 Nov\nOpening Account Balance...", "1": "", "2": "$0.00"}, {"0": "22 Dec\nClosing Account Balance...", "1": "", "2": "$0.00"}, {"0": "...W", "1": "", "2": ""}, {"0": "Account Name:", "1": "Q M SHAW-WILLIAMS", "2": ""}, {"0": "Product Name:", "1": "Fast Forward Saver Account", "2": ""}, {"0": "Personalised Name:", "1": "TAX holding", "2": ""}, {"0": "Account Number:", "1": "38-9004-0646977-05", "2": ""}, {"0": "Statement Period:", "1": "23 November 2024 to 22 December 2024", "2": ""}, {"0": ".", "1": "", "2": ""}, {"0": "Date\nTransaction", "1": "<PERSON><PERSON><PERSON><PERSON>\nDe<PERSON>", "2": "Balance"}, {"0": "23 Nov\nOpening Account Balance...", "1": "", "2": "$0.00"}, {"0": "22 Dec\nClosing Account Balance...", "1": "", "2": "$0.00"}], "metadata": {"accuracy": 95.52, "whitespace": 36.67, "order": 1, "dimensions": "20x3", "flavor": "stream"}}