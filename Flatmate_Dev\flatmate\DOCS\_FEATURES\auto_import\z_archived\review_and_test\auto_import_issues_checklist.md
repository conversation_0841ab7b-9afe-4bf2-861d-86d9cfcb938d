# Auto Import Feature: Issues & UX Checklist

---

## High Priority – Easy Fixes
auto import functions
~- [ ] On auto_import archive folder set, save location should default to that folder~

- [ ] If not set, save location should stay "same as source"
- [ ] Imported files should stay in the import folder
- [ ] There should be immediate feedback in the center panel
- [ ] ud_data left panel: Auto import should have a checkbox and label, enabled by default unless no source folder set in
- [ ] Option in source location should just be 'auto_import'
- [ ] Select button should appear only if no option set, or if auto import is disabled and no source set
- [ ] Update database checkbox is a hacked-in label and checkbox, not using app base widgets (user_test1) - gui shared components
---

## Auto Import Functionality
## resolved:
- [x] Could not browse to set the folder (user_test1)
- [x] Nothing seemed functional in the stand-in GUI (user_test1)
- [x] Info bar message: must restart app for change to take effect (user_test1)
- [x] After restart, Source Files option displays `Set auto import folder...` (user_test1)
- [x] Save location displays `Same as source files...` (user_test1)
- [x] Info bar says `save location: None` (user_test1) now has useful info
- [x] No new folders created in Downloads after operation (user_test1)
- [x] Double-up on infobars: ud_data is still creating its old prototype version (user_test1)
- [x] Unsure how to proceed after initial steps (user_test1)
- [x] Priority: get auto import logic working and feedback in the center panel (user_test1)
- [x] System works, CSVs successfully imported, shifted to designated archive folder (auto_import_testing)

### todo:
- [ ] Could stick with original set source folder logic (auto_import_testing)
-  If auto import is enabled, the last used folder is set to the auto import folder (auto_import_testing)

- [ ] center panel info widget: source folder: <path> auto import status: enabled
last import stats: 
database stats
database last updated
accounts:
 <account number><account name (if_exists)> <current to:> <date> <last updated> etc 
 #### this begs the question, how do we best verify current to date? extract creation date from statements? - should be day before creation date of statement ...


## Auto Import UX
 [x] New prototype pop-up GUI works (auto_import_testing)
- [ ] GUI implementation seems illogical in flow for the user (user_test1)
- [ ] Unused right panel intended for context-specific options/settings (user_test1)
- [ ] Center panel is mostly empty except for stand-in welcome message (user_test1)

### general:
- [ ]Update_data : Table view could be in center, if  switching modules would swap panels (user_test1)
- [ ] Nav bar/side bar could act as a task context switcher (user_test1)
- [ ] Need to apply good GUI design principles and logical flow (user_test1)
-
- [ ] Check existing GUI infrastructure for feedback functionality (auto_import_testing)

## Update Data Function
- [ ] App should open at update_data module (auto_import_testing) if recent update detected
- [ ] App should open at update_data module (auto_import_testing) if new files detected in auto import folder
- save loacation should be the output folder
a master csv should be kept in the output folder if master_csv created


## Update Data UX
- [ ] There should be a poll activated on app startup: 'new files detected, update database?' (auto_import_testing)
- [ ] Update master CSV should be an option (auto_import_testing)
- [ ] Update database should be an option (auto_import_testing)
- [ ] Both should use GUI shared components: checkbox and label (inline) (auto_import_testing)

## General Functionality
- [ ] System works (auto_import_testing)

## General UX
- [ ] Nav side bar could be on the left ?
- [ ] Should have an expanded state with inline text labels for each option (for new users) or tooltips (auto_import_testing)
- [ ] What is now left panel in categorise modules should be right panel and be collapsible upward, maximising space for spreadsheets (auto_import_testing)

# Export functionality 
- output column names for export funcitonality should have option to use original names 



*Source: user_test1_notes.md*
*appended with general app notes*
---
*Add further issues from user_test2_notes.md if present.*
