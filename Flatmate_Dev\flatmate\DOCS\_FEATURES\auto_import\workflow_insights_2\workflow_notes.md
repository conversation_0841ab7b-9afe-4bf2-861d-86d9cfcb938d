well it seems to run like
we have a planning session
which starts with a discussion and
reports - arhitectural - gui implications 
effected codebase
patterns
once we feel ike we have a handle 
we create the implementation docs
a prd and or hit list 
a design doc
an implemetnation guide 
a task list
and a workflow insights folder 
and a final review
then 
we actually attempt to implement 
make changes to the code base
then their is a review where look at what worked and what needs ot be resolved
the problem has been we have been getting nested folders
What we want is to update the implementaiton docs 
document the changes and state of e the code base 
plan our next assualt as it were... but by modifying / updating the existing docs
- prefferably
this is the part of the process I'm interested in right now 
to complicate matters
we may need to start a new chat 






we run those design docs
then we see how we did 
Then we review
review 1 review 2 etc 
until we ar hpapy with the implimentation

