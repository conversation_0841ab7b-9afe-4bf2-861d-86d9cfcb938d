"""
Base Widget

Base widget class following the App-Wide Widget Pattern established in the codebase.
Provides consistent API for all shared widgets with configuration management,
content handling, style loading, and runtime flexibility.
"""

from abc import ABC<PERSON>eta
from typing import Any, Optional
from PySide6.QtWidgets import QWidget
from PySide6.QtCore import Signal, QObject

from ..config.widget_config import BaseWidgetConfig
from ..styles.loader import StyleLoader


class QtABCMeta(type(QObject), ABCMeta):
    """Metaclass that combines Qt's QObject metaclass with Python's ABCMeta."""
    pass


class BaseWidget(QWidget, metaclass=QtABCMeta):
    """Base widget following App-Wide Widget Pattern.
    
    Provides consistent API for all shared widgets with:
    - Configuration management
    - Content handling  
    - Style loading
    - Runtime flexibility
    """
    
    # Standard signals that subclasses can override
    configuration_changed = Signal(dict)
    content_changed = Signal()
    
    def __init__(self, parent=None):
        """Initialize with sensible defaults."""
        super().__init__(parent)
        self._config = self._get_default_config()
        self._content = None
        self._is_shown = False
        self._setup_ui()
        self._apply_default_styles()
    
    # === ABSTRACT METHODS (Must be implemented by subclasses) ===
    
    @_get_default_config.register
    def _get_default_config(self) -> BaseWidgetConfig:
        """Return default configuration for this widget type."""
        raise NotImplementedError("Subclasses must implement _get_default_config")
    
    @_setup_ui.register
    def _setup_ui(self):
        """Initialize UI components."""
        raise NotImplementedError("Subclasses must implement _setup_ui")
    
    @_apply_configuration.register
    def _apply_configuration(self):
        """Apply current configuration to UI."""
        raise NotImplementedError("Subclasses must implement _apply_configuration")
    
    @_apply_content.register
    def _apply_content(self):
        """Apply current content to UI."""
        raise NotImplementedError("Subclasses must implement _apply_content")
    
    # === CONFIGURATION (Instance Runtime Defaults) ===
    
    def configure(self, **kwargs) -> 'BaseWidget':
        """Configure widget behavior and appearance.
        
        Args:
            **kwargs: Configuration options specific to widget type
            
        Returns:
            self for method chaining
        """
        for key, value in kwargs.items():
            if hasattr(self._config, key):
                setattr(self._config, key, value)
            else:
                raise ValueError(f"Unknown configuration option: {key}")
        
        # Apply configuration if already shown
        if self._is_shown:
            self._apply_configuration()
            
        self.configuration_changed.emit(kwargs)
        return self
    
    # === CONTENT ===
    
    def set_content(self, content: Any) -> 'BaseWidget':
        """Set widget content.
        
        Args:
            content: Content to display (type varies by widget)
            
        Returns:
            self for method chaining
        """
        self._content = content
        
        if self._is_shown:
            self._apply_content()
            
        self.content_changed.emit()
        return self
    
    # === VISIBILITY ===
    
    def show(self):
        """Show widget and apply all configurations."""
        if not self._is_shown:
            self._apply_configuration()
            self._apply_content()
            self._is_shown = True
        super().show()
    
    def hide(self):
        """Hide widget."""
        self._is_shown = False
        super().hide()
    
    # === STYLE MANAGEMENT ===
    
    def _apply_default_styles(self):
        """Apply default styles for this widget type."""
        widget_type = self.__class__.__name__.lower().replace('widget', '')
        if widget_type.endswith('button'):
            widget_type = 'buttons'
        elif widget_type.endswith('checkbox'):
            widget_type = 'checkboxes'
        elif widget_type.endswith('label'):
            widget_type = 'labels'
        elif 'option' in widget_type or 'menu' in widget_type:
            widget_type = 'option_menus'
        elif 'selector' in widget_type:
            widget_type = 'selectors'
        elif 'filter' in widget_type:
            widget_type = 'filters'
        
        StyleLoader.apply_widget_styles(self, widget_type)
    
    def refresh_styles(self):
        """Refresh widget styles (useful for theme changes)."""
        self._apply_default_styles()
    
    # === UTILITY METHODS ===
    
    def get_config(self) -> BaseWidgetConfig:
        """Get current configuration (read-only)."""
        return self._config
    
    def get_content(self) -> Any:
        """Get current content."""
        return self._content
    
    def reset_to_defaults(self) -> 'BaseWidget':
        """Reset widget to default configuration."""
        self._config = self._get_default_config()
        if self._is_shown:
            self._apply_configuration()
        return self
