# PDF Processing Pipeline Design

## Overview
A two-stage pipeline for processing bank statement PDFs into standardised transaction records.

## Pipeline Architecture

### Stage 1: PDF Parser (`pdf_parser_0.1.py`)
**Purpose**: Extract raw table data and identify account boundaries
**Input**: PDF file
**Output**: Structured JSON with account metadata and raw transaction data

#### Key Responsibilities:
1. Extract all tables from PDF using Camelot
2. Identify account header patterns (Account Name, Account Number, Product Name, etc.)
3. Associate subsequent transaction tables with the most recent account header
4. Preserve all metadata for downstream processing
5. Output structured intermediate format optimised for parsing

#### Output Format (JSON):
```json
{
  "extraction_metadata": {
    "pdf_path": "path/to/file.pdf",
    "extraction_timestamp": "2024-12-23T10:30:00Z",
    "total_tables": 10,
    "camelot_version": "0.10.1"
  },
  "accounts": [
    {
      "account_metadata": {
        "account_name": "Q M SHAW-WILLIAMS",
        "account_number": "38-9004-0646977-00",
        "product_name": "Free Up Account",
        "personalised_name": "CURRENT",
        "statement_period": "23 November 2024 to 22 December 2024"
      },
      "raw_transactions": [
        {
          "table_number": 3,
          "row_data": ["23 Nov", "Opening Account Balance...", "", "", "$0.83"],
          "row_type": "master|continuation|metadata"
        }
      ]
    }
  ]
}
```

### Stage 2: Output Parser (`output_parser_0.1.py`)
**Purpose**: Process structured data into clean transaction records
**Input**: JSON from PDF Parser
**Output**: Clean CSV with standardised transactions

#### Key Responsibilities:
1. Parse the structured JSON from Stage 1
2. Consolidate fragmented transaction rows (master + continuation)
3. Apply account metadata to each transaction
4. Generate standardised transaction records
5. Output clean CSV ready for database ingestion

#### Output Format (CSV):
```csv
account_number,account_name,date,description,withdrawals,deposits,balance,transaction_type,metadata
38-9004-0646977-00,Q M SHAW-WILLIAMS,2024-11-23,Opening Account Balance,,,0.83,opening_balance,"{""product_name"":""Free Up Account""}"
```

## Account Recognition Logic

### Header Pattern Detection:
- Look for rows containing "Account Name:", "Account Number:", "Product Name:"
- Extract metadata from subsequent rows until transaction header found
- Transaction headers identified by "Date,Transaction,Withdrawals,Deposits,Balance" pattern

### Account Boundary Rules:
1. New account header = new account scope
2. Transaction tables without headers inherit from most recent account header
3. Non-transaction tables (summaries, etc.) are catalogued but not processed

## Development Approach
- **Version 0.1**: Basic extraction and parsing
- **Version 0.2**: Enhanced account recognition
- **Version 0.3**: Error handling and edge cases
- Each version documented with test results and metadata
