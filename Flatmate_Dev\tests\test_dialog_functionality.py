#!/usr/bin/env python3
"""
Test script to verify AutoImportConfigDialog functionality.
This will help identify why the browse buttons aren't working.
"""

import os
import sys
from pathlib import Path

# Add the flatmate source directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'flatmate', 'src'))

from PySide6.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget
from PySide6.QtCore import Qt

def test_dialog_standalone():
    """Test the dialog in standalone mode"""
    print("Testing AutoImportConfigDialog standalone...")
    
    try:
        from fm.gui.dialogs.auto_import_config_dialog import AutoImportConfigDialog
        
        app = QApplication([])
        
        # Create a simple main window to parent the dialog
        main_window = QMainWindow()
        main_window.setWindowTitle("Dialog Test")
        main_window.resize(400, 300)
        
        # Create a button to open the dialog
        central_widget = QWidget()
        layout = QVBoxLayout(central_widget)
        
        open_dialog_btn = QPushButton("Open Auto-Import Dialog")
        layout.addWidget(open_dialog_btn)
        
        main_window.setCentralWidget(central_widget)
        
        def open_dialog():
            print("Opening dialog...")
            dialog = AutoImportConfigDialog(main_window)
            result = dialog.exec()
            print(f"Dialog result: {result}")
            if result == AutoImportConfigDialog.DialogCode.Accepted:
                print("Dialog was accepted")
            else:
                print("Dialog was cancelled")
        
        open_dialog_btn.clicked.connect(open_dialog)
        
        main_window.show()
        print("Main window shown. Click the button to test the dialog.")
        print("Close the main window to exit.")
        
        # Don't use app.exec() in automated testing
        return True
        
    except Exception as e:
        print(f"Error testing dialog: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_qfiledialog_basic():
    """Test basic QFileDialog functionality"""
    print("Testing basic QFileDialog...")
    
    try:
        from PySide6.QtWidgets import QFileDialog
        
        # Test if QFileDialog can be imported and created
        print("QFileDialog import successful")
        
        # Test static method (this is what the dialog uses)
        print("QFileDialog.getExistingDirectory method exists:", hasattr(QFileDialog, 'getExistingDirectory'))
        
        return True
        
    except Exception as e:
        print(f"Error testing QFileDialog: {e}")
        return False

def test_config_integration():
    """Test configuration system integration"""
    print("Testing configuration integration...")
    
    try:
        from fm.core.config import config
        from fm.core.config.keys import ConfigKeys
        
        # Test if config keys exist
        print("AutoImport config keys exist:", hasattr(ConfigKeys, 'AutoImport'))
        
        if hasattr(ConfigKeys, 'AutoImport'):
            print("ENABLED key exists:", hasattr(ConfigKeys.AutoImport, 'ENABLED'))
            print("IMPORT_PATH key exists:", hasattr(ConfigKeys.AutoImport, 'IMPORT_PATH'))
            
            # Test getting/setting values
            current_enabled = config.get_value(ConfigKeys.AutoImport.ENABLED, False)
            print(f"Current enabled status: {current_enabled}")
        
        return True
        
    except Exception as e:
        print(f"Error testing config integration: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    print("=== Auto-Import Dialog Functionality Test ===")
    print()
    
    # Test 1: Basic QFileDialog
    print("1. Testing QFileDialog basic functionality...")
    qfd_result = test_qfiledialog_basic()
    print(f"   Result: {'PASS' if qfd_result else 'FAIL'}")
    print()
    
    # Test 2: Configuration integration
    print("2. Testing configuration integration...")
    config_result = test_config_integration()
    print(f"   Result: {'PASS' if config_result else 'FAIL'}")
    print()
    
    # Test 3: Dialog standalone (requires manual interaction)
    print("3. Testing dialog standalone...")
    dialog_result = test_dialog_standalone()
    print(f"   Result: {'PASS' if dialog_result else 'FAIL'}")
    print()
    
    print("=== Test Summary ===")
    print(f"QFileDialog: {'PASS' if qfd_result else 'FAIL'}")
    print(f"Config Integration: {'PASS' if config_result else 'FAIL'}")
    print(f"Dialog Creation: {'PASS' if dialog_result else 'FAIL'}")
    
    if all([qfd_result, config_result, dialog_result]):
        print("\nAll basic tests passed. The issue may be in the application context.")
        print("Try running the dialog from within the actual application.")
    else:
        print("\nSome tests failed. Check the error messages above.")
