# Camelot PDF Parser Research

## Overview
Camelot is a Python library for extracting tables from PDF files. It provides two extraction methods: 'lattice' (for tables with clearly defined borders) and 'stream' (for tables without clear borders, using character positioning).

## Installation
```bash
pip install camelot-py[cv]
```

Dependencies:
- Python 3.6+
- Ghostscript
- OpenCV (for image-based PDFs)
- Tkinter (for GUI)

## Core API

### Basic Usage
```python
import camelot

# Extract tables from a PDF
tables = camelot.read_pdf('file.pdf', flavor='stream')

# Access the first table
table = tables[0]

# Get the table as a pandas DataFrame
df = table.df

# Export to various formats
table.to_csv('table.csv')
table.to_json('table.json')
table.to_excel('table.xlsx')
table.to_html('table.html')
```

### Key Methods and Properties

#### `camelot.read_pdf()`
Main function to extract tables from PDFs.

**Important Parameters:**
- `filepath`: Path to the PDF file
- `pages`: Page numbers to extract tables from (e.g., '1,3,4' or '1-3')
- `flavor`: Extraction method ('lattice' or 'stream')
- `table_areas`: List of table coordinates to extract
- `process_background`: Process background graphics (useful for colored tables)
- `line_scale`: Line size scaling factor (for lattice)
- `strip_text`: Characters to strip from text (default: '\\n')

#### `TableList` Object
The result of `camelot.read_pdf()` is a `TableList` object, which is a list-like collection of `Table` objects.

**Methods:**
- `tables.n`: Number of tables extracted
- `tables[i]`: Access table at index i
- `tables.export('filename.format')`: Export all tables

#### `Table` Object
Represents a single extracted table.

**Properties:**
- `table.df`: Pandas DataFrame representation
- `table.parsing_report`: Dictionary with quality metrics
- `table.shape`: Table dimensions (rows, columns)
- `table.cells`: List of cell objects with coordinates
- `table.flavor`: Extraction method used ('lattice' or 'stream')

**Methods:**
- `table.to_csv()`: Export to CSV
- `table.to_json()`: Export to JSON
- `table.to_excel()`: Export to Excel
- `table.to_html()`: Export to HTML
- `table.to_markdown()`: Export to Markdown

### Parsing Report
Each table has a `parsing_report` property with quality metrics:

```python
{
    'accuracy': 99.02,  # Extraction accuracy percentage
    'whitespace': 12.24,  # Percentage of whitespace
    'order': 1,  # Table number in reading order
    'page': 1  # Page number
}
```

## Output Structure

### DataFrame Structure
The primary output is a pandas DataFrame (`table.df`):
- Each cell in the table is a cell in the DataFrame
- Headers are not automatically detected (first row is not treated as header)
- Empty cells are represented as empty strings

### JSON Structure (when using `to_json()`)
```json
{
  "0": {
    "0": "Row1Col1",
    "1": "Row1Col2"
  },
  "1": {
    "0": "Row2Col1",
    "1": "Row2Col2"
  }
}
```

To get a more useful JSON structure:
```python
json_data = table.df.to_json(orient='records')
```

This produces:
```json
[
  {"0": "Row1Col1", "1": "Row1Col2"},
  {"0": "Row2Col1", "1": "Row2Col2"}
]
```

## Advanced Features

### Table Detection
Camelot doesn't automatically detect table areas. You need to specify them:

```python
tables = camelot.read_pdf('file.pdf', flavor='stream', table_areas=['0,700,800,100'])
```

### Table Filtering
Filter tables based on accuracy:

```python
tables = camelot.read_pdf('file.pdf')
high_quality_tables = [table for table in tables if table.parsing_report['accuracy'] > 90]
```

### Multi-page Processing
Process multiple pages:

```python
tables = camelot.read_pdf('file.pdf', pages='1-3')
```

## Strengths and Weaknesses

### Strengths
- Highly accurate for well-structured tables
- Two complementary extraction methods (lattice and stream)
- Rich quality metrics for each table
- Easy export to various formats
- Good handling of complex table structures

### Weaknesses
- No automatic table detection
- Requires manual tuning for optimal results
- Slower than some alternatives
- Requires external dependencies (Ghostscript)
- No built-in OCR (needs pre-processing with Tesseract)

## Integration with Flatmate

For integrating Camelot with the Flatmate project:

```python
from fm.core.services.logger import log

def extract_tables_from_pdf(pdf_path):
    try:
        log.info(f"Extracting tables from {pdf_path} using Camelot")
        tables = camelot.read_pdf(pdf_path, flavor='stream', pages='all')
        log.info(f"Found {len(tables)} tables")
        
        result = []
        for i, table in enumerate(tables):
            if table.parsing_report['accuracy'] > 80:
                result.append(table.df)
                log.info(f"Table {i+1} extracted with accuracy: {table.parsing_report['accuracy']:.2f}%")
            else:
                log.warning(f"Table {i+1} skipped due to low accuracy: {table.parsing_report['accuracy']:.2f}%")
        
        return result
    except Exception as e:
        log.error(f"Error extracting tables: {str(e)}")
        return []
```

## Resources
- [Official Documentation](https://camelot-py.readthedocs.io/)
- [GitHub Repository](https://github.com/camelot-dev/camelot)
- [PyPI Package](https://pypi.org/project/camelot-py/)
