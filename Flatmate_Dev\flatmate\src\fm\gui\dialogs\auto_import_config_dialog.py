#!/usr/bin/env python3
"""
Auto Import Configuration Dialog

Provides a simple configuration interface for the auto-import folder feature.
Allows users to enable/disable the feature and configure folder paths.
"""

from pathlib import Path
from typing import Optional

from PySide6.QtCore import Qt
from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
    QLabel, QLineEdit, QPushButton, QCheckBox, 
    QFileDialog, QMessageBox, QGroupBox, QTextEdit
)

from fm.core.config import config
from fm.core.config.keys import ConfigKeys
from fm.core.services.logger import log


class AutoImportConfigDialog(QDialog):
    """Configuration dialog for auto-import folder feature"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Auto-Import Configuration")
        self.setModal(True)
        self.setMinimumWidth(500)
        self.setMinimumHeight(400)
        
        # Store original values for cancel functionality
        self.original_enabled = config.get_value(ConfigKeys.AutoImport.ENABLED, False)
        self.original_import_path = config.get_value(ConfigKeys.AutoImport.IMPORT_PATH, "")
        self.original_archive_path = config.get_value(ConfigKeys.AutoImport.ARCHIVE_PATH, "")
        self.original_failed_path = config.get_value(ConfigKeys.AutoImport.FAILED_PATH, "")
        
        self.setup_ui()
        self.load_current_settings()
    
    def setup_ui(self):
        """Set up the user interface"""
        layout = QVBoxLayout(self)
        
        # Title and description
        title_label = QLabel("Auto-Import Folder Configuration")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; margin-bottom: 10px;")
        layout.addWidget(title_label)
        
        description = QLabel(
            "Configure automatic importing of CSV files from a designated folder. "
            "When enabled, the application will monitor the specified folder for new CSV files "
            "and automatically process them through the import pipeline."
        )
        description.setWordWrap(True)
        description.setStyleSheet("color: #666; margin-bottom: 15px;")
        layout.addWidget(description)
        
        # Enable/Disable checkbox
        self.enabled_checkbox = QCheckBox("Enable Auto-Import")
        self.enabled_checkbox.setStyleSheet("font-weight: bold; margin-bottom: 10px;")
        self.enabled_checkbox.toggled.connect(self._on_enabled_changed)
        layout.addWidget(self.enabled_checkbox)
        
        # Configuration group
        self.config_group = QGroupBox("Folder Configuration")
        config_layout = QFormLayout(self.config_group)
        
        # Import folder path
        import_layout = QHBoxLayout()
        self.import_path_edit = QLineEdit()
        self.import_path_edit.setPlaceholderText("Select folder to monitor for CSV files...")
        self.import_browse_btn = QPushButton("Browse...")
        self.import_browse_btn.clicked.connect(self._browse_import_folder)
        import_layout.addWidget(self.import_path_edit)
        import_layout.addWidget(self.import_browse_btn)
        config_layout.addRow("Import Folder:", import_layout)
        
        # Archive folder path
        archive_layout = QHBoxLayout()
        self.archive_path_edit = QLineEdit()
        self.archive_path_edit.setPlaceholderText("Folder for successfully processed files...")
        self.archive_browse_btn = QPushButton("Browse...")
        self.archive_browse_btn.clicked.connect(self._browse_archive_folder)
        archive_layout.addWidget(self.archive_path_edit)
        archive_layout.addWidget(self.archive_browse_btn)
        config_layout.addRow("Archive Folder:", archive_layout)
        
        # Failed folder path
        failed_layout = QHBoxLayout()
        self.failed_path_edit = QLineEdit()
        self.failed_path_edit.setPlaceholderText("Folder for failed import files...")
        self.failed_browse_btn = QPushButton("Browse...")
        self.failed_browse_btn.clicked.connect(self._browse_failed_folder)
        failed_layout.addWidget(self.failed_path_edit)
        failed_layout.addWidget(self.failed_browse_btn)
        config_layout.addRow("Failed Imports:", failed_layout)
        
        layout.addWidget(self.config_group)
        
        # Status information
        status_group = QGroupBox("Current Status")
        status_layout = QVBoxLayout(status_group)
        
        self.status_text = QTextEdit()
        self.status_text.setMaximumHeight(100)
        self.status_text.setReadOnly(True)
        status_layout.addWidget(self.status_text)
        
        layout.addWidget(status_group)
        
        # Buttons
        button_layout = QHBoxLayout()
        
        self.create_folders_btn = QPushButton("Create Default Folders")
        self.create_folders_btn.clicked.connect(self._create_default_folders)
        button_layout.addWidget(self.create_folders_btn)
        
        button_layout.addStretch()
        
        self.ok_btn = QPushButton("OK")
        self.ok_btn.clicked.connect(self.accept)
        self.ok_btn.setDefault(True)
        button_layout.addWidget(self.ok_btn)
        
        self.cancel_btn = QPushButton("Cancel")
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_btn)
        
        layout.addLayout(button_layout)
        
        # Update initial state
        self._update_ui_state()
    
    def load_current_settings(self):
        """Load current configuration values into the UI"""
        self.enabled_checkbox.setChecked(self.original_enabled)
        self.import_path_edit.setText(self.original_import_path)
        self.archive_path_edit.setText(self.original_archive_path)
        self.failed_path_edit.setText(self.original_failed_path)
        self._update_status()
    
    def _on_enabled_changed(self, enabled: bool):
        """Handle enable/disable checkbox change"""
        self._update_ui_state()
        self._update_status()
    
    def _update_ui_state(self):
        """Update UI state based on enabled checkbox"""
        enabled = self.enabled_checkbox.isChecked()
        # Always allow configuration - users need to set paths before enabling
        # Only disable the create folders button when disabled
        self.config_group.setEnabled(True)  # Always allow path configuration
        self.create_folders_btn.setEnabled(enabled)
    
    def _update_status(self):
        """Update the status text area"""
        if not self.enabled_checkbox.isChecked():
            self.status_text.setText("Auto-import is disabled. Configure paths below, then enable to activate monitoring.")
            return
        
        import_path = self.import_path_edit.text().strip()
        if not import_path:
            self.status_text.setText("No import folder configured.")
            return
        
        # Check if paths exist
        status_lines = []
        
        import_path_obj = Path(import_path)
        if import_path_obj.exists():
            status_lines.append(f"✓ Import folder exists: {import_path}")
        else:
            status_lines.append(f"⚠ Import folder does not exist: {import_path}")
        
        archive_path = self.archive_path_edit.text().strip()
        if archive_path:
            archive_path_obj = Path(archive_path)
            if archive_path_obj.exists():
                status_lines.append(f"✓ Archive folder exists: {archive_path}")
            else:
                status_lines.append(f"⚠ Archive folder does not exist: {archive_path}")
        
        failed_path = self.failed_path_edit.text().strip()
        if failed_path:
            failed_path_obj = Path(failed_path)
            if failed_path_obj.exists():
                status_lines.append(f"✓ Failed folder exists: {failed_path}")
            else:
                status_lines.append(f"⚠ Failed folder does not exist: {failed_path}")
        
        self.status_text.setText("\n".join(status_lines))
    
    def _browse_import_folder(self):
        """Browse for import folder"""
        current_path = self.import_path_edit.text().strip()
        initial_dir = current_path if current_path and Path(current_path).exists() else str(Path.home() / "Downloads")
        
        folder = QFileDialog.getExistingDirectory(
            self,
            "Select Import Folder",
            initial_dir
        )
        
        if folder:
            self.import_path_edit.setText(folder)
            # Auto-populate archive and failed paths if they're empty
            if not self.archive_path_edit.text().strip():
                self.archive_path_edit.setText(str(Path(folder) / "archive"))
            if not self.failed_path_edit.text().strip():
                self.failed_path_edit.setText(str(Path(folder) / "failed_imports"))
            self._update_status()
    
    def _browse_archive_folder(self):
        """Browse for archive folder"""
        current_path = self.archive_path_edit.text().strip()
        initial_dir = current_path if current_path and Path(current_path).exists() else self.import_path_edit.text().strip()
        
        folder = QFileDialog.getExistingDirectory(
            self,
            "Select Archive Folder",
            initial_dir
        )
        
        if folder:
            self.archive_path_edit.setText(folder)
            self._update_status()
    
    def _browse_failed_folder(self):
        """Browse for failed imports folder"""
        current_path = self.failed_path_edit.text().strip()
        initial_dir = current_path if current_path and Path(current_path).exists() else self.import_path_edit.text().strip()
        
        folder = QFileDialog.getExistingDirectory(
            self,
            "Select Failed Imports Folder",
            initial_dir
        )
        
        if folder:
            self.failed_path_edit.setText(folder)
            self._update_status()
    
    def _create_default_folders(self):
        """Create default folder structure"""
        downloads_path = Path.home() / "Downloads"
        default_import_path = downloads_path / "flatmate_imports"
        
        try:
            # Create main import folder
            default_import_path.mkdir(parents=True, exist_ok=True)
            
            # Create subfolders
            (default_import_path / "archive").mkdir(exist_ok=True)
            (default_import_path / "failed_imports").mkdir(exist_ok=True)
            
            # Update UI
            self.import_path_edit.setText(str(default_import_path))
            self.archive_path_edit.setText(str(default_import_path / "archive"))
            self.failed_path_edit.setText(str(default_import_path / "failed_imports"))
            
            self._update_status()
            
            QMessageBox.information(
                self,
                "Folders Created",
                f"Default folder structure created at:\n{default_import_path}"
            )
            
        except Exception as e:
            QMessageBox.warning(
                self,
                "Error Creating Folders",
                f"Failed to create default folders:\n{str(e)}"
            )
    
    def accept(self):
        """Save settings and close dialog"""
        try:
            # Validate paths if enabled
            if self.enabled_checkbox.isChecked():
                import_path = self.import_path_edit.text().strip()
                if not import_path:
                    QMessageBox.warning(self, "Invalid Configuration", "Import folder path is required when auto-import is enabled.")
                    return
                
                # Validate that import path exists or can be created
                import_path_obj = Path(import_path)
                if not import_path_obj.exists():
                    reply = QMessageBox.question(
                        self,
                        "Create Folder?",
                        f"Import folder does not exist:\n{import_path}\n\nCreate it now?",
                        QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
                    )
                    if reply == QMessageBox.StandardButton.Yes:
                        try:
                            import_path_obj.mkdir(parents=True, exist_ok=True)
                        except Exception as e:
                            QMessageBox.warning(self, "Error", f"Failed to create import folder:\n{str(e)}")
                            return
                    else:
                        return
            
            # Save configuration
            config.set_value(ConfigKeys.AutoImport.ENABLED, self.enabled_checkbox.isChecked())
            config.set_value(ConfigKeys.AutoImport.IMPORT_PATH, self.import_path_edit.text().strip())
            config.set_value(ConfigKeys.AutoImport.ARCHIVE_PATH, self.archive_path_edit.text().strip())
            config.set_value(ConfigKeys.AutoImport.FAILED_PATH, self.failed_path_edit.text().strip())
            
            log.info("Auto-import configuration saved")
            
            # Show restart message if settings changed
            if (self.enabled_checkbox.isChecked() != self.original_enabled or
                self.import_path_edit.text().strip() != self.original_import_path):
                QMessageBox.information(
                    self,
                    "Settings Saved",
                    "Auto-import settings have been saved.\n\nRestart the application for changes to take effect."
                )
            
            super().accept()
            
        except Exception as e:
            log.error(f"Error saving auto-import configuration: {e}")
            QMessageBox.critical(self, "Error", f"Failed to save configuration:\n{str(e)}")
    
    def reject(self):
        """Cancel without saving"""
        super().reject()
