# PDF Processing Pipeline v0.1

## Overview
A two-stage, iterative pipeline for processing bank statement PDFs into standardised transaction records with full account metadata preservation.

**Problem Solved**: Bank statement PDFs contain multiple accounts with fragmented transaction data. Each transaction must be tagged with its account metadata for downstream database ingestion.

## Pipeline Architecture

### Stage 1: PDF Parser (`pdf_parser_0.1.py`)
**Purpose**: Extract tables and identify account boundaries  
**Input**: PDF file  
**Output**: Structured JSON with account metadata and raw transactions

```bash
python pdf_parser_0.1.py input.pdf output.json
```

### Stage 2: Output Parser (`output_parser_0.1.py`)
**Purpose**: Process structured data into clean transaction records  
**Input**: JSON from Stage 1  
**Output**: Clean CSV with standardised transactions

```bash
python output_parser_0.1.py input.json output.csv
```

## Key Features

### Account Recognition
- Automatically identifies account headers (Account Name, Number, Product Name)
- Associates subsequent transaction tables with correct account metadata
- Handles multiple accounts per PDF

### Transaction Consolidation
- Merges fragmented transaction rows (master + continuation rows)
- Preserves all transaction metadata and descriptions
- Maintains source traceability

### Standardised Output
- Each transaction tagged with full account metadata
- Ready for database ingestion
- Includes processing timestamps and source references

## Output Format

Final CSV contains:
- `account_number`, `account_name`, `product_name`
- `date`, `description`, `withdrawals`, `deposits`, `balance`
- `table_number`, `source_rows`, `processing_timestamp`

## Development Approach
- **v0.1**: Basic extraction and parsing
- **v0.2**: Enhanced account recognition
- **v0.3**: Error handling and edge cases

Each version is documented with test results and metadata.

## Files
- `pdf_parser_0.1.py` - Stage 1: PDF extraction
- `output_parser_0.1.py` - Stage 2: Data processing
- `PIPELINE_DESIGN.md` - Detailed technical specification
- `output/` - Generated files and test results
