# Camelot Output Parsing Analysis

## Overview
This document outlines the pattern identified in the CSV files generated by the Camelot PDF parsing tool and proposes a logic for programmatically cleaning the data.

## The Problem
The raw CSV output from Camelot often contains fragmented data, where a single logical transaction is split across multiple rows. This makes direct analysis impossible.

## Identified Pattern

Through analysis of `camelot_parser_output_table_3.csv`, a clear pattern emerges for identifying and reassembling transactions:

1.  **Master Row**: This is the primary row for a transaction. It is the only row in a transaction group that contains:
    *   A `Date`.
    *   A numeric value in either the `Withdrawals` or `Deposits` column.
    *   A numeric value in the `Balance` column.

2.  **Continuation Rows**: These are subsequent rows that belong to the preceding Master Row. They contain descriptive information and can be identified by the following characteristics:
    *   They may or may not have a `Date`.
    *   The `Withdrawals`, `Deposits`, and `Balance` columns are either empty or contain non-numeric text (e.g., "PERIODIC PAY").

## Proposed Parsing Logic

A script can be developed to parse this structure by iterating through the CSV and applying the following rules:

1.  Iterate through each row of the source CSV.
2.  If a row is identified as a **Master Row**, treat it as the beginning of a new transaction.
3.  Continue iterating and concatenate the 'Transaction' description from all subsequent **Continuation Rows** onto the description of the current Master Row.
4.  When another **Master Row** is encountered, the previous transaction is considered complete. The process then repeats.
5.  The final output should be a clean dataset with one row per logical transaction.
