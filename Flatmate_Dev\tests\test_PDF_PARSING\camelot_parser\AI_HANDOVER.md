# AI Handover: Flatmate PDF Statement Parsing Pipeline (v0.1)

## Purpose
This document provides a concise, structured summary of the Flatmate PDF parsing pipeline, optimised for AI systems and future maintainers. It covers architecture, file locations, data flow, key logic, and extension points.

---

## Pipeline Overview
- **Goal**: Convert multi-account PDF bank statements into standardised, metadata-rich transaction records ready for unified database ingestion.
- **Approach**: Two-stage pipeline
    1. **PDF Parser**: Extracts tables, detects account boundaries, outputs structured JSON
    2. **Output Parser**: Consolidates transaction rows, applies account metadata, outputs clean CSV

---

## Key Files & Locations
- **Pipeline root**: `tests/test_PDF_PARSING/camelot_parser/`
- **Stage 1**: `pdf_parser_0.1.py` (PDF → structured JSON)
- **Stage 2**: `output_parser_0.1.py` (JSON → clean CSV)
- **Design Doc**: `PIPELINE_DESIGN.md`
- **Test Results**: `TEST_RESULTS_v0.1.md`
- **README**: `README.md`
- **Output folder**: `output/` (holds all generated files)

---

## Data Flow
1. **Input**: Multi-account PDF statement (e.g. `tests/test_pdfs/kiwibank/2024-Dec-23_Personal.pdf`)
2. **Stage 1**: `pdf_parser_0.1.py input.pdf output/structured_data_v0.1.json`
    - Extracts all tables using Camelot
    - Identifies account headers (Account Name, Number, Product Name, etc.)
    - Associates transaction tables with the most recent account header
    - Outputs: `output/structured_data_v0.1.json`
3. **Stage 2**: `output_parser_0.1.py output/structured_data_v0.1.json output/final_transactions_v0.1.csv`
    - Consolidates fragmented transaction rows (master + continuation)
    - Applies account metadata to every transaction
    - Outputs: `output/final_transactions_v0.1.csv`

---

## Core Logic (Summary)
### Account Recognition
- Detects account headers by matching rows containing: `Account Name:`, `Account Number:`, `Product Name:`, etc.
- Each new header starts a new account scope
- Transaction tables without explicit headers inherit metadata from the most recent account

### Transaction Consolidation
- Transaction tables are parsed row-by-row
- "Master" rows: contain monetary values (withdrawal, deposit, or balance)
- "Continuation" rows: description fragments, appended to the previous master row
- All rows are tagged with `row_type` for downstream processing

### Metadata Preservation
- Every transaction record includes:
    - Account number, account name, product name, personalised name, statement period
    - Source table and row indices for traceability
    - Processing timestamp
    - Full original description (with all fragments merged)

---

## Output Formats
### Structured JSON (Intermediate)
- Top-level: extraction metadata, list of accounts
- Each account: metadata + raw transaction rows (with row type tags)

### Final CSV
- Columns: `account_number`, `account_name`, `product_name`, `personalised_name`, `statement_period`, `date`, `description`, `withdrawals`, `deposits`, `balance`, `table_number`, `source_rows`, `processing_timestamp`
- One row per consolidated transaction

---

## Versioning & Extensibility
- **Versioned scripts**: `_0.1.py` suffix, upgrade path for future improvements
- **Easy extension**: Add new logic for account header detection, error handling, or output formats in new versions
- **Documentation**: All changes and test results tracked in markdown files in the same folder

---

## Test Results & Validation
- See `TEST_RESULTS_v0.1.md` for sample outputs, metrics, and validation
- Pipeline tested on real Kiwibank PDF: 5 accounts identified, 23 transactions extracted with full metadata

---

## AI/Developer Guidance
- **Separation of concerns**: Never merge PDF parsing and output logic; always maintain a clean pipeline boundary
- **Preserve all metadata**: Never discard or overwrite account or transaction context
- **Optimise for downstream parsing**: Structured JSON should be as explicit and lossless as possible
- **Version all changes**: Increment file suffixes and update documentation for every meaningful change
- **Test with real PDFs**: Always validate on multi-account statements

---

## Extension Points
- Improved account header detection (regex, fuzzy matching)
- Multi-table per account handling
- Error handling/reporting for malformed PDFs
- Support for additional banks/formats (add new handlers)
- Integration with unified database ingestion pipeline

---

## Contacts
- For questions or onboarding, see project lead or refer to `PIPELINE_DESIGN.md` for deep technical details.

---

**End of AI Handover v0.1**
