# Future Development & Deferred Features

**Date**: 2025-07-22
**Status**: Logged

---

This document tracks features, ideas, and implementation details that have been deferred from the current "Update Data" view refactoring project. They will be considered for implementation in future development cycles.

## 1. Options & Settings Panel

-   **Concept:** A dedicated, application-wide "Options and Settings" panel or screen.
-   **Purpose:** To house less frequently used configuration options and settings, keeping the main contextual UI panels clean and focused on primary actions.
-   **Deferred Items for this Panel:**
    -   Any advanced or non-essential controls related to the auto-import or file processing workflows.
it will be contextual to the current module - perhaps to current workflow mode 
## 2. Unimplemented Details from Legacy Auto-Import Popup

-   **Concept:** The original auto-import configuration popup dialog contained several settings and options.
-   **Action:** A review of the original popup's functionality is needed to identify any valuable configuration options that were not brought into the new, integrated design.
-   **Examples might include:**  post-import actions, etc.
-   **Plan:** These identified options will be considered for inclusion in the future "Options & Settings Panel".
They are largely arounf the "archive folder"

flowchart TD
    subgraph Database_Mode["Database Mode (Update Database ✓)"]
      A1[App Starts or User Switches to Database Mode]
      A2[Show Auto-Import Controls]
      A3[Show Archive Folder (Save Location) Control]
      A4[Show File Tree of New Files]
      A5[Show Dashboard: Status, Recent Activity, DB Stats]
      A6[User Clicks 'Update Database']
      A7[Import New Files to Database, Archive Originals]
      A8[Show Success/Errors, Update Dashboard]
      A1 --> A2 --> A3 --> A4 --> A5 --> A6 --> A7 --> A8
    end

    subgraph File_Utility_Mode["File Utility Mode (Update Database ☐)"]
      B1[App Starts or User Switches to File Utility Mode]
      B2[Show Source Files Panel]
      B3[Show Save Location Panel]
      B4[Show File Staging Area (Tree View)]
      B5[User Selects Files, Clicks 'Process Files']
      B6[Process/Merge Files, Save Output]
      B7[Show Success/Errors, Update File List]
      B1 --> B2 --> B3 --> B4 --> B5 --> B6 --> B7
    end