#!/usr/bin/env python3
"""
Script to extract detailed information from PDF using pdfplumber.
This script extracts text, tables, words with positioning, and character data.
"""

import os
import sys
import json
import pandas as pd
import pdfplumber

def ensure_dir(directory):
    """Ensure directory exists."""
    if not os.path.exists(directory):
        os.makedirs(directory)

def extract_pdfplumber_data(pdf_path, output_dir):
    """Extract detailed data using pdfplumber and save outputs."""
    print(f"Extracting data from {pdf_path} using pdfplumber...")
    
    with pdfplumber.open(pdf_path) as pdf:
        # Process each page
        for i, page in enumerate(pdf.pages):
            page_num = i + 1
            print(f"Processing page {page_num}...")
            
            # Extract and save text
            text = page.extract_text()
            if text:
                text_path = os.path.join(output_dir, f"pdfplumber_text_page_{page_num}.txt")
                with open(text_path, 'w', encoding='utf-8') as f:
                    f.write(text)
                print(f"Saved text from page {page_num}: {text_path}")
            else:
                print(f"No text found on page {page_num}")
            
            # Extract and save words with positioning
            words = page.extract_words()
            if words:
                words_path = os.path.join(output_dir, f"pdfplumber_words_page_{page_num}.json")
                with open(words_path, 'w') as f:
                    json.dump(words, f, indent=2)
                print(f"Saved {len(words)} words with positioning from page {page_num}: {words_path}")
            else:
                print(f"No words found on page {page_num}")
            
            # Extract and save tables
            tables = page.extract_tables()
            if tables:
                print(f"Found {len(tables)} tables on page {page_num}")
                for j, table_data in enumerate(tables):
                    table_num = j + 1
                    
                    # Convert to DataFrame
                    df = pd.DataFrame(table_data)
                    
                    # Use first row as header if it looks like a header
                    if not df.empty:
                        df.columns = df.iloc[0]
                        df = df[1:]
                    
                    # Save as CSV
                    csv_path = os.path.join(output_dir, f"pdfplumber_table_page_{page_num}_table_{table_num}.csv")
                    df.to_csv(csv_path, index=False)
                    print(f"Saved table {table_num} from page {page_num} as CSV: {csv_path}")
                    
                    # Save as JSON
                    json_path = os.path.join(output_dir, f"pdfplumber_table_page_{page_num}_table_{table_num}.json")
                    
                    # Convert DataFrame to JSON
                    json_data = {
                        'table_data': json.loads(df.to_json(orient='records')),
                        'metadata': {
                            'page': page_num,
                            'dimensions': f"{df.shape[0]}x{df.shape[1]}",
                            'raw_data': table_data  # Include the raw list of lists
                        }
                    }
                    
                    with open(json_path, 'w') as f:
                        json.dump(json_data, f, indent=2)
                    print(f"Saved table {table_num} from page {page_num} as JSON: {json_path}")
            else:
                print(f"No tables found on page {page_num}")
            
            # Extract and save character data
            chars = page.chars
            if chars:
                chars_path = os.path.join(output_dir, f"pdfplumber_chars_page_{page_num}.json")
                with open(chars_path, 'w') as f:
                    json.dump(chars, f, indent=2)
                print(f"Saved {len(chars)} characters with metadata from page {page_num}: {chars_path}")
            else:
                print(f"No character data found on page {page_num}")
            
            # Extract and save lines
            lines = page.lines
            if lines:
                lines_path = os.path.join(output_dir, f"pdfplumber_lines_page_{page_num}.json")
                with open(lines_path, 'w') as f:
                    json.dump(lines, f, indent=2)
                print(f"Saved {len(lines)} lines from page {page_num}: {lines_path}")
            
            # Extract and save rectangles
            rects = page.rects
            if rects:
                rects_path = os.path.join(output_dir, f"pdfplumber_rects_page_{page_num}.json")
                with open(rects_path, 'w') as f:
                    json.dump(rects, f, indent=2)
                print(f"Saved {len(rects)} rectangles from page {page_num}: {rects_path}")
        
        # Save PDF metadata
        metadata = pdf.metadata
        if metadata:
            meta_path = os.path.join(output_dir, "pdfplumber_metadata.json")
            with open(meta_path, 'w') as f:
                json.dump(metadata, f, indent=2)
            print(f"Saved PDF metadata: {meta_path}")

def main():
    """Main function."""
    if len(sys.argv) < 2:
        print("Usage: python pdfplumber_detailed_test.py <pdf_path>")
        sys.exit(1)
    
    pdf_path = sys.argv[1]
    output_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 
                             "test_parser_outputs", "pdfplumber_test_1")
    
    ensure_dir(output_dir)
    extract_pdfplumber_data(pdf_path, output_dir)
    
    print("\nAll pdfplumber outputs saved to:", output_dir)

if __name__ == "__main__":
    main()
