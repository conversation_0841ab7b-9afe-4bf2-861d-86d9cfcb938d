#!/usr/bin/env python3
"""
Tests for AutoImportManager functionality

Tests the auto-import folder feature including configuration loading,
file monitoring setup, and integration with dw_director pipeline.
"""

import os
import sys
import tempfile
import time
import unittest
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

# Add the flatmate source directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'flatmate', 'src'))

from fm.core.services.auto_import_manager import AutoImportManager
from fm.core.config import config
from fm.core.config.keys import ConfigKeys


class TestAutoImportManager(unittest.TestCase):
    """Test cases for AutoImportManager"""
    
    def setUp(self):
        """Set up test environment"""
        # Reset singleton instance for each test
        AutoImportManager._instance = None
        
        # Create temporary directories for testing
        self.temp_dir = tempfile.mkdtemp()
        self.import_path = Path(self.temp_dir) / "import"
        self.archive_path = Path(self.temp_dir) / "archive"
        self.failed_path = Path(self.temp_dir) / "failed"
        
        # Create test directories
        self.import_path.mkdir(parents=True, exist_ok=True)
        self.archive_path.mkdir(parents=True, exist_ok=True)
        self.failed_path.mkdir(parents=True, exist_ok=True)
        
        # Mock configuration
        self.config_values = {
            ConfigKeys.AutoImport.ENABLED: True,
            ConfigKeys.AutoImport.IMPORT_PATH: str(self.import_path),
            ConfigKeys.AutoImport.ARCHIVE_PATH: str(self.archive_path),
            ConfigKeys.AutoImport.FAILED_PATH: str(self.failed_path)
        }
    
    def tearDown(self):
        """Clean up test environment"""
        # Clean up temporary directory
        import shutil
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
        
        # Reset singleton
        AutoImportManager._instance = None
    
    @patch('fm.core.services.auto_import_manager.config.get_value')
    def test_singleton_pattern(self, mock_get_value):
        """Test that AutoImportManager follows singleton pattern"""
        mock_get_value.side_effect = lambda key, default=None: self.config_values.get(key, default)

        # Create two instances
        manager1 = AutoImportManager()
        manager2 = AutoImportManager()

        # Should be the same instance
        self.assertIs(manager1, manager2)

    @patch('fm.core.services.auto_import_manager.config.get_value')
    def test_configuration_loading(self, mock_get_value):
        """Test that configuration is loaded correctly"""
        mock_get_value.side_effect = lambda key, default=None: self.config_values.get(key, default)

        manager = AutoImportManager()

        # Check that configuration was loaded
        self.assertTrue(manager.enabled)
        self.assertEqual(manager.import_path, self.import_path)
        self.assertEqual(manager.archive_path, self.archive_path)
        self.assertEqual(manager.failed_path, self.failed_path)

    @patch('fm.core.services.auto_import_manager.config.get_value')
    def test_disabled_configuration(self, mock_get_value):
        """Test behavior when auto-import is disabled"""
        disabled_config = self.config_values.copy()
        disabled_config[ConfigKeys.AutoImport.ENABLED] = False
        mock_get_value.side_effect = lambda key, default=None: disabled_config.get(key, default)

        manager = AutoImportManager()

        # Should be disabled
        self.assertFalse(manager.enabled)

        # Start should not do anything
        manager.start()
        self.assertIsNone(manager.observer)
        self.assertFalse(manager.running)

    @patch('fm.core.services.auto_import_manager.config.get_value')
    @patch('fm.core.services.auto_import_manager.Observer')
    @patch('fm.core.services.auto_import_manager.threading.Thread')
    def test_start_monitoring(self, mock_thread, mock_observer, mock_get_value):
        """Test starting file system monitoring"""
        mock_get_value.side_effect = lambda key, default=None: self.config_values.get(key, default)
        
        # Mock observer and thread
        mock_observer_instance = Mock()
        mock_observer.return_value = mock_observer_instance
        mock_thread_instance = Mock()
        mock_thread.return_value = mock_thread_instance
        
        manager = AutoImportManager()
        manager.start()
        
        # Check that monitoring was started
        self.assertTrue(manager.running)
        self.assertIsNotNone(manager.observer)
        self.assertIsNotNone(manager.worker_thread)
        
        # Check that observer was configured and started
        mock_observer_instance.schedule.assert_called_once()
        mock_observer_instance.start.assert_called_once()
        
        # Check that worker thread was started
        mock_thread_instance.start.assert_called_once()
    
    @patch('fm.core.services.auto_import_manager.config.get_value')
    def test_queue_file_for_processing(self, mock_get_value):
        """Test queuing files for processing"""
        mock_get_value.side_effect = lambda key, default=None: self.config_values.get(key, default)

        manager = AutoImportManager()
        test_file = str(self.import_path / "test.csv")

        # Queue a file
        manager.queue_file_for_processing(test_file)

        # Check that file was added to queue
        self.assertFalse(manager.processing_queue.empty())
        queued_file = manager.processing_queue.get()
        self.assertEqual(queued_file, test_file)

    @patch('fm.core.services.auto_import_manager.config.get_value')
    def test_queue_full_handling(self, mock_get_value):
        """Test handling of full processing queue"""
        mock_get_value.side_effect = lambda key, default=None: self.config_values.get(key, default)

        manager = AutoImportManager()

        # Fill the queue to capacity (maxsize=100)
        for i in range(100):
            manager.queue_file_for_processing(f"test_{i}.csv")

        # Try to add one more (should be dropped)
        with patch('fm.core.services.logger.log') as mock_log:
            manager.queue_file_for_processing("overflow.csv")
            mock_log.error.assert_called()

    @patch('fm.core.services.auto_import_manager.config.get_value')
    @patch('fm.modules.update_data.utils.dw_director.dw_director')
    def test_process_single_file_success(self, mock_dw_director, mock_get_value):
        """Test successful processing of a single file"""
        mock_get_value.side_effect = lambda key, default=None: self.config_values.get(key, default)
        
        # Mock successful dw_director response
        mock_dw_director.return_value = {"status": "success", "message": "File processed successfully"}
        
        # Create a test file
        test_file = self.import_path / "test.csv"
        test_file.write_text("date,description,amount\n2024-01-01,Test transaction,100.00")
        
        manager = AutoImportManager()
        
        # Mock the move methods to avoid actual file operations
        with patch.object(manager, '_move_to_archive') as mock_archive:
            manager._process_single_file(str(test_file))
            
            # Check that dw_director was called
            mock_dw_director.assert_called_once()
            
            # Check that file was moved to archive
            mock_archive.assert_called_once_with(test_file)
    
    @patch('fm.core.services.auto_import_manager.config.get_value')
    @patch('fm.modules.update_data.utils.dw_director.dw_director')
    def test_process_single_file_failure(self, mock_dw_director, mock_get_value):
        """Test handling of file processing failure"""
        mock_get_value.side_effect = lambda key, default=None: self.config_values.get(key, default)

        # Mock failed dw_director response
        mock_dw_director.return_value = {"status": "error", "message": "Invalid file format"}

        # Create a test file
        test_file = self.import_path / "test.csv"
        test_file.write_text("invalid,csv,content")

        manager = AutoImportManager()

        # Mock the move methods to avoid actual file operations
        with patch.object(manager, '_move_to_failed') as mock_failed:
            manager._process_single_file(str(test_file))

            # Check that dw_director was called
            mock_dw_director.assert_called_once()

            # Check that file was moved to failed folder
            mock_failed.assert_called_once_with(test_file, "Invalid file format")

    @patch('fm.core.services.auto_import_manager.config.get_value')
    def test_move_to_archive(self, mock_get_value):
        """Test moving files to archive folder"""
        mock_get_value.side_effect = lambda key, default=None: self.config_values.get(key, default)

        # Create a test file
        test_file = self.import_path / "test.csv"
        test_file.write_text("test content")

        manager = AutoImportManager()
        manager._move_to_archive(test_file)

        # Check that original file is gone
        self.assertFalse(test_file.exists())

        # Check that file exists in archive with timestamp
        archive_files = list(self.archive_path.glob("test_*.csv"))
        self.assertEqual(len(archive_files), 1)
        self.assertTrue(archive_files[0].name.startswith("test_"))
        self.assertTrue(archive_files[0].name.endswith(".csv"))

    @patch('fm.core.services.auto_import_manager.config.get_value')
    def test_move_to_failed(self, mock_get_value):
        """Test moving files to failed folder"""
        mock_get_value.side_effect = lambda key, default=None: self.config_values.get(key, default)
        
        # Create a test file
        test_file = self.import_path / "test.csv"
        test_file.write_text("test content")
        
        manager = AutoImportManager()
        error_message = "Test error message"
        manager._move_to_failed(test_file, error_message)
        
        # Check that original file is gone
        self.assertFalse(test_file.exists())
        
        # Check that file exists in failed folder with timestamp and FAILED suffix
        failed_files = list(self.failed_path.glob("test_*_FAILED.csv"))
        self.assertEqual(len(failed_files), 1)
        
        # Check that error log file was created
        error_log_files = list(self.failed_path.glob("test_*_FAILED.csv.error.txt"))
        self.assertEqual(len(error_log_files), 1)
        
        # Check error log content
        error_log_content = error_log_files[0].read_text()
        self.assertIn("Test error message", error_log_content)


if __name__ == '__main__':
    unittest.main()
