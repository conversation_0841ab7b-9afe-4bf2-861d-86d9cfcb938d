# Auto-Import Implementation Guide

**Updated**: 2025-01-22 (Post-Architecture Implementation)
**Status**: ARCHITECTURAL_IMPLEMENTATION_COMPLETE

## Executive Summary
This guide provides the exact implementation steps for the **"Declarative Mode-Driven UI Architecture"** morphic UI approach. The core architecture has been implemented using Pydantic models for centralized mode state management. Current focus is on resolving remaining UI issues and conducting user testing.

## Implementation Overview
**Strategy**: Declarative Mode-Driven UI Architecture (COMPLETED)
**Approach**: Centralized Pydantic models define UI states, context manager applies configurations
**Key Insight**: Single source of truth eliminates scattered UI logic and prevents state inconsistencies
**Architecture**: State Pattern + Configuration Pattern + Single Source of Truth

## Current Implementation Status

### ✅ COMPLETED (January 22, 2025)
- **Centralized Mode System** - Pydantic models define all UI states
- **Context Manager Integration** - Single responsibility for mode determination + UI application
- **Auto-import Detection** - App polls folder on startup and detects pending files
- **Application Stability** - No crashes, clean startup, proper module loading

### ❌ REMAINING ISSUES
- **Database Checkbox Styling** - Black background issue, needs GUI shared widgets
- **Mode Switching Logic** - Checkbox doesn't properly switch between modes
- **Save Location Display** - Selected location not showing in UI

### 🔄 NEXT IMPLEMENTATION FOCUS
Priority 1: Fix UI styling and mode switching logic
Priority 2: Conduct comprehensive user testing
Priority 3: Address any issues discovered during testing

## Phase 1: Configuration Extension

### 1.1 Add Auto-Import Configuration Keys
**File**: `flatmate/src/fm/modules/update_data/config/ud_keys.py`

```python
# Add to existing UpdateDataKeys class
class AutoImportKeys:
    """Auto-import configuration keys."""
    MODE = "auto_import.mode"  # 'database' | 'file_only'
    SOURCE_LOCATIONS = "auto_import.source_locations"  # List[str]
    MASTER_CSV_ENABLED = "auto_import.master_csv.enabled"  # bool
    PROCESSED_FILES_DB = "auto_import.processed_files_db"  # str (path to state db)
    LAST_SCAN_TIME = "auto_import.last_scan_time"  # timestamp
    PENDING_FILES = "auto_import.pending_files"  # List[dict]
```

### 1.2 Update Default Configuration
**File**: `flatmate/src/fm/modules/update_data/config/defaults.yaml`

```yaml
# Add to existing defaults
auto_import:
  mode: "database"  # or "file_only"
  source_locations: []  # List of paths to monitor
  master_csv:
    enabled: false
  processed_files_db: "~/.flatmate/auto_import_state.json"
  last_scan_time: null
  pending_files: []
```

## Phase 2: File State Repository

### 2.1 Create File State Models
**File**: `flatmate/src/fm/modules/update_data/auto_import/models.py`

```python
from dataclasses import dataclass
from pathlib import Path
from typing import Dict, List, Optional
import hashlib
import json
from datetime import datetime

@dataclass
class FileState:
    """Represents the state of a file for auto-import."""
    path: str
    hash: str
    size: int
    last_modified: float
    processed: bool = False
    processed_at: Optional[datetime] = None
    error_message: Optional[str] = None

    @classmethod
    def from_path(cls, file_path: str) -> 'FileState':
        """Create FileState from file path."""
        path = Path(file_path)
        stat = path.stat()
        
        # Calculate file hash
        hasher = hashlib.md5()
        with open(path, 'rb') as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hasher.update(chunk)
        
        return cls(
            path=str(path.absolute()),
            hash=hasher.hexdigest(),
            size=stat.st_size,
            last_modified=stat.st_mtime
        )

    def to_dict(self) -> Dict:
        """Convert to dictionary for JSON storage."""
        return {
            'path': self.path,
            'hash': self.hash,
            'size': self.size,
            'last_modified': self.last_modified,
            'processed': self.processed,
            'processed_at': self.processed_at.isoformat() if self.processed_at else None,
            'error_message': self.error_message
        }

    @classmethod
    def from_dict(cls, data: Dict) -> 'FileState':
        """Create FileState from dictionary."""
        return cls(
            path=data['path'],
            hash=data['hash'],
            size=data['size'],
            last_modified=data['last_modified'],
            processed=data.get('processed', False),
            processed_at=datetime.fromisoformat(data['processed_at']) if data.get('processed_at') else None,
            error_message=data.get('error_message')
        )
```

### 2.2 Create File State Repository
**File**: `flatmate/src/fm/modules/update_data/auto_import/file_state.py`

```python
import json
from pathlib import Path
from typing import Dict, List, Optional
from .models import FileState

class FileStateRepository:
    """Manages persistent file state for auto-import."""
    
    def __init__(self, db_path: str):
        self.db_path = Path(db_path).expanduser()
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        self._ensure_db_exists()
    
    def _ensure_db_exists(self):
        """Create database file if it doesn't exist."""
        if not self.db_path.exists():
            self.db_path.write_text(json.dumps({"files": {}}))
    
    def _load_state(self) -> Dict[str, Dict]:
        """Load the entire state from disk."""
        try:
            return json.loads(self.db_path.read_text())
        except (json.JSONDecodeError, FileNotFoundError):
            return {"files": {}}
    
    def _save_state(self, state: Dict[str, Dict]):
        """Save the entire state to disk."""
        self.db_path.write_text(json.dumps(state, indent=2))
    
    def get_file_state(self, file_path: str) -> Optional[FileState]:
        """Get the state for a specific file."""
        state = self._load_state()
        file_data = state["files"].get(str(Path(file_path).absolute()))
        return FileState.from_dict(file_data) if file_data else None
    
    def update_file_state(self, file_state: FileState):
        """Update or create file state."""
        state = self._load_state()
        state["files"][file_state.path] = file_state.to_dict()
        self._save_state(state)
    
    def get_pending_files(self, source_locations: List[str]) -> List[FileState]:
        """Get all pending files from source locations."""
        pending = []
        state = self._load_state()
        
        for location in source_locations:
            location_path = Path(location)
            if not location_path.exists():
                continue
            
            # Find all CSV files
            for csv_file in location_path.rglob("*.csv"):
                file_state = self.get_file_state(str(csv_file))
                
                # If file doesn't exist in state or has changed
                if not file_state:
                    new_state = FileState.from_path(str(csv_file))
                    pending.append(new_state)
                elif not file_state.processed:
                    # Check if file has been modified since last scan
                    current_state = FileState.from_path(str(csv_file))
                    if current_state.hash != file_state.hash:
                        # File has changed, treat as new
                        pending.append(current_state)
        
        return pending
    
    def mark_processed(self, file_path: str, error: Optional[str] = None):
        """Mark a file as processed."""
        file_state = self.get_file_state(file_path)
        if file_state:
            file_state.processed = True
            file_state.processed_at = datetime.now()
            file_state.error_message = error
            self.update_file_state(file_state)
```

## Phase 3: Enhanced File Discovery

### 3.1 Extend View Context Manager
**File**: `flatmate/src/fm/modules/update_data/view_context_manager.py`

```python
# Add imports at top
from pathlib import Path
from typing import List, Dict
from .auto_import.file_state import FileStateRepository
from ...core.config import config
from ...core.config.keys import ConfigKeys

# Add to UpdateDataViewManager class
class UpdateDataViewManager:
    def __init__(self):
        # Existing code...
        self.file_repo = None
    
    def _get_file_repository(self) -> FileStateRepository:
        """Get or create file state repository."""
        if self.file_repo is None:
            db_path = config.get_value(
                ConfigKeys.AutoImport.PROCESSED_FILES_DB,
                "~/.flatmate/auto_import_state.json"
            )
            self.file_repo = FileStateRepository(db_path)
        return self.file_repo
    
    def get_auto_import_status(self) -> dict:
        """Enhanced auto-import status with pending files."""
        from ...core.config import config
        
        enabled = config.get_value(ConfigKeys.AutoImport.ENABLED, False)
        source_locations = config.get_value(ConfigKeys.AutoImport.SOURCE_LOCATIONS, [])
        mode = config.get_value(ConfigKeys.AutoImport.MODE, "database")
        
        if not enabled or not source_locations:
            return {
                'enabled': enabled,
                'mode': mode,
                'source_locations': source_locations,
                'pending_files': []
            }
        
        # Get pending files using repository
        repo = self._get_file_repository()
        pending_states = repo.get_pending_files(source_locations)
        
        return {
            'enabled': enabled,
            'mode': mode,
            'source_locations': source_locations,
            'pending_files': [state.path for state in pending_states],
            'pending_count': len(pending_states)
        }
    
    def _scan_for_pending_files(self, import_path: str) -> list:
        """Enhanced scanning with state management."""
        # This method becomes deprecated - use get_auto_import_status instead
        return []
```

## Phase 4: UI Integration

### 4.1 Update Presenter Integration
**File**: `flatmate/src/fm/modules/update_data/ud_presenter.py`

```python
# Add import
from .view_context_manager import UpdateDataViewManager

# Add to UpdateDataPresenter.__init__
class UpdateDataPresenter:
    def __init__(self, main_window, gui_config=None, gui_keys=None):
        # Existing code...
        self.view_manager = UpdateDataViewManager()
    
    def _refresh_content(self, **params):
        """Enhanced refresh with auto-import integration."""
        log.debug("Refreshing UpdateData content")
        
        # Set the source option from config
        last_source_option = ud_config.get_value(
            UpdateDataKeys.Source.LAST_SOURCE_OPTION, 
            default=SourceOptions.SELECT_FOLDER.value
        )
        self.view.set_source_option(last_source_option)
        
        # Configure view based on auto-import status
        auto_import_status = self.view_manager.get_auto_import_status()
        mode = auto_import_status.get('mode', 'database')
        is_database_mode = mode == 'database'
        
        self.view_manager.configure_view_for_workflow(
            self.view, 
            is_database_mode=is_database_mode,
            auto_import_status=auto_import_status
        )
        
        # Show appropriate message
        if auto_import_status.get('pending_files'):
            self.view.show_info_message(
                f"Found {auto_import_status['pending_count']} pending files for import"
            )
        
        # Update UI based on mode
        if is_database_mode:
            self.view.show_database_mode()
        else:
            self.view.show_file_utility_mode()
```

### 4.2 Update View Context Manager Configuration
**File**: `flatmate/src/fm/modules/update_data/view_context_manager.py`

```python
# Add to configure_view_for_workflow method
def configure_view_for_workflow(self, view, is_database_mode=True, auto_import_status=None):
    """Enhanced configuration with auto-import integration."""
    # Existing database mode configuration
    if is_database_mode:
        self._configure_database_mode(view, auto_import_status)
    else:
        self._configure_file_utility_mode(view, auto_import_status)

def _configure_database_mode(self, view, auto_import_status=None):
    """Configure view for database mode with auto-import."""
    # Existing database mode setup
    view.set_left_panel_visible(True)
    view.set_center_panel_visible(True)
    view.set_right_panel_visible(True)
    
    # Auto-import specific
    if auto_import_status and auto_import_status.get('pending_files'):
        view.set_auto_import_indicator(True)
        view.set_pending_files_count(auto_import_status['pending_count'])
    else:
        view.set_auto_import_indicator(False)

def _configure_file_utility_mode(self, view, auto_import_status=None):
    """Configure view for file utility mode."""
    # Existing file utility mode setup
    view.set_left_panel_visible(True)
    view.set_center_panel_visible(False)
    view.set_right_panel_visible(False)
    
    # Auto-import specific
    if auto_import_status and auto_import_status.get('pending_files'):
        view.set_pending_files_list(auto_import_status['pending_files'])
```

## Phase 5: Testing Integration

### 5.1 Create Test File
**File**: `flatmate/tests/test_auto_import.py`

```python
import pytest
import tempfile
import json
from pathlib import Path
from flatmate.src.fm.modules.update_data.auto_import.models import FileState
from flatmate.src.fm.modules.update_data.auto_import.file_state import FileStateRepository

class TestFileState:
    def test_file_state_creation(self):
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
            f.write("test,data\n1,2")
            temp_path = f.name
        
        try:
            state = FileState.from_path(temp_path)
            assert state.path == str(Path(temp_path).absolute())
            assert state.size > 0
            assert len(state.hash) == 32  # MD5 hash
        finally:
            Path(temp_path).unlink()

class TestFileStateRepository:
    def test_repository_operations(self):
        with tempfile.TemporaryDirectory() as temp_dir:
            db_path = Path(temp_dir) / "test_state.json"
            repo = FileStateRepository(str(db_path))
            
            # Test file state management
            test_file = Path(temp_dir) / "test.csv"
            test_file.write_text("test,data\n1,2")
            
            # Create state
            state = FileState.from_path(str(test_file))
            repo.update_file_state(state)
            
            # Retrieve state
            retrieved = repo.get_file_state(str(test_file))
            assert retrieved is not None
            assert retrieved.hash == state.hash
            
            # Mark processed
            repo.mark_processed(str(test_file))
            processed = repo.get_file_state(str(test_file))
            assert processed.processed is True
```

## Phase 6: Configuration Migration

### 6.1 Migration Utility
**File**: `flatmate/src/fm/modules/update_data/auto_import/migration.py`

```python
from flatmate.src.fm.core.config import config
from flatmate.src.fm.core.config.keys import ConfigKeys

def migrate_auto_import_config():
    """Migrate existing auto-import configuration to new format."""
    # Check if old format exists
    old_import_path = config.get_value(ConfigKeys.AutoImport.IMPORT_PATH, None)
    
    if old_import_path:
        # Migrate to new format
        new_config = {
            ConfigKeys.AutoImport.ENABLED: True,
            ConfigKeys.AutoImport.MODE: "database",
            ConfigKeys.AutoImport.SOURCE_LOCATIONS: [old_import_path],
            ConfigKeys.AutoImport.MASTER_CSV_ENABLED: False
        }
        
        for key, value in new_config.items():
            config.set_value(key, value)
        
        # Remove old key
        config.remove_key(ConfigKeys.AutoImport.IMPORT_PATH)
        
        return True
    
    return False
```

## Implementation Checklist

### Pre-Development
- [ ] Create feature branch: `feature/auto-import-enhancement`
- [ ] Verify all existing patterns work correctly
- [ ] Set up test environment

### Development Session
- [ ] **Phase 1**: Configuration extension (5 min)
- [ ] **Phase 2**: File state models and repository (15 min)
- [ ] **Phase 3**: Enhanced file discovery integration (10 min)
- [ ] **Phase 4**: UI integration with view manager (15 min)
- [ ] **Phase 5**: Testing and validation (10 min)
- [ ] **Phase 6**: Configuration migration (5 min)

### Post-Development
- [ ] Run full test suite
- [ ] Verify configuration migration
- [ ] Test UI state transitions
- [ ] Feature flag configuration

## Code Review Checklist

### Pattern Consistency
- [ ] Uses existing MVP pattern
- [ ] Follows event-driven architecture
- [ ] Leverages existing configuration system
- [ ] Integrates with view manager

### Integration Quality
- [ ] Seamless with existing file processing
- [ ] No breaking changes to existing API
- [ ] Configuration migration handled
- [ ] Feature flag ready

### Testing
- [ ] Unit tests for file state management
- [ ] Integration tests for UI integration
- [ ] Configuration migration tests
- [ ] End-to-end workflow tests

## Ready for Implementation

The codebase is ready for a single development session implementation. All patterns, integration points, and testing strategies are defined. The implementation extends existing capabilities rather than creating new architecture.
