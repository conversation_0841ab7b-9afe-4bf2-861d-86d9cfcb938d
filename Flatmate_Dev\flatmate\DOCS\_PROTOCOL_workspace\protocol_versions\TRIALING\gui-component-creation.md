---
description: "GUI component creation workflow following industry standards"
type: "specialized_workflow"
version: "1.0"
agents: ["windsurf", "augment", "kilo", "claude", "any"]
integration: "Uses unified-work-session.md for session management"
---

# GUI Component Creation Workflow

**Purpose**: Create GUI components following industry-standard patterns  
**Integration**: Use with unified work session protocol (REFACTOR or FEATURE type)  
**Standards**: Based on React, Vue, Angular component patterns  

---

## Pre-Creation Analysis

### **Session Setup**
1. **Follow**: `unified-work-session.md` for session setup
2. **Session Type**: FEATURE (new component) or REFACTOR (updating existing)
3. **Create**: Component planning documentation

### **Component Classification** (5 minutes)
```
What am I creating?
├─ Foundation class that others inherit from → BASE CLASS
│   └─ Location: gui/_shared_components/base/[category]/
├─ Complete, reusable UI component → REUSABLE COMPONENT  
│   └─ Location: gui/_shared_components/[component_name]/
└─ Helper function or utility → UTILITY
    └─ Location: gui/_shared_components/utils/
```

### **Requirements Analysis** (10 minutes)
```markdown
## Component Requirements

### Purpose
**What does this component do?**: [Clear description]
**Why is it needed?**: [Business/technical justification]
**Who will use it?**: [Target users/developers]

### Classification
**Type**: [Base Class | Reusable Component | Utility]
**Category**: [Widget | Toolbar | Panel | Layout | etc.]
**Complexity**: [Simple | Medium | Complex]

### Dependencies
**Inherits from**: [Base classes or Qt classes]
**Requires**: [Other components, services, data]
**Integrates with**: [Existing systems, protocols]
```

---

## Component Design Phase

### **Architecture Analysis** (15 minutes)
**Use codebase retrieval to understand existing patterns**

#### Actions:
- [ ] **Analyze similar components** - How are comparable components structured?
- [ ] **Identify base classes** - What should this inherit from?
- [ ] **Review naming conventions** - Follow established patterns
- [ ] **Check integration points** - How will this work with existing code?
- [ ] **Identify configuration needs** - What should be configurable?

#### Documentation:
```markdown
## Architecture Analysis

### Similar Components
**[Component 1]**: [How it's structured, what patterns it uses]
**[Component 2]**: [Relevant patterns and approaches]

### Base Classes Available
**[BaseClass 1]**: [What it provides, when to use]
**[BaseClass 2]**: [Capabilities and use cases]

### Integration Points
**Configuration**: [How component will be configured]
**Styling**: [How theming will be applied]
**Signals**: [What events component will emit]
**Data**: [How component will receive/manage data]
```

### **Interface Design** (10 minutes)
```markdown
## Component Interface

### Public API
**Constructor**: `ComponentName(config=None, parent=None)`
**Key Methods**:
- `set_data(data)` - [Purpose]
- `get_selection()` - [Purpose]
- `update_display()` - [Purpose]

### Signals
**[signal_name]** = Signal([type]) - [When emitted, what data]
**[another_signal]** = Signal([type]) - [Purpose and usage]

### Configuration
**Required Settings**: [Settings that must be provided]
**Optional Settings**: [Settings with defaults]
**Validation Rules**: [How settings are validated]
```

---

## Implementation Phase

### **Phase 1: Structure Creation** (10 minutes)
```bash
# Create component folder structure
mkdir -p "gui/_shared_components/[component_name]"
cd "gui/_shared_components/[component_name]"

# Create standard files
touch __init__.py
touch [component_name].py
touch config.py

# Create subfolders if needed
mkdir -p components tests styles utils
```

### **Phase 2: Configuration Class** (15 minutes)
```python
# config.py template
from fm.core.config import BaseConfig

class ComponentNameConfig(BaseConfig):
    """Configuration for ComponentName."""
    
    def __init__(self):
        super().__init__()
        
        # UI Configuration
        self.setting_1 = default_value
        self.setting_2 = default_value
        
        # Behavior Configuration
        self.behavior_setting = default_value
    
    def validate(self):
        """Validate configuration values."""
        if self.setting_1 < 0:
            raise ValueError("setting_1 must be non-negative")
        
        # Additional validation logic
```

### **Phase 3: Main Component Class** (30-45 minutes)
```python
# [component_name].py template
from PySide6.QtWidgets import QWidget
from PySide6.QtCore import Signal
from ..base.widgets.base_widget import BaseWidget
from .config import ComponentNameConfig

class ComponentName(BaseWidget):
    """[Component description and purpose]."""
    
    # Component signals
    signal_name = Signal(str)  # [Description of when emitted]
    
    def __init__(self, config: ComponentNameConfig = None, parent=None):
        super().__init__(parent)
        self.config = config or ComponentNameConfig()
        self.config.validate()
        
        self._init_ui()
        self._connect_signals()
        self._apply_styling()
    
    def _init_ui(self):
        """Initialize component UI."""
        # UI creation logic
        pass
    
    def _connect_signals(self):
        """Connect component signals."""
        # Signal connection logic
        pass
    
    def _apply_styling(self):
        """Apply component styling."""
        # Styling logic
        pass
    
    # Public API methods
    def set_data(self, data):
        """Set component data."""
        pass
    
    def get_selection(self):
        """Get current selection."""
        pass
```

### **Phase 4: Testing** (20 minutes)
```python
# tests/test_[component_name].py
import pytest
from PySide6.QtWidgets import QApplication
from ..[component_name] import ComponentName, ComponentNameConfig

class TestComponentName:
    def test_initialization(self):
        """Test component initializes correctly."""
        component = ComponentName()
        assert component is not None
        assert isinstance(component.config, ComponentNameConfig)
    
    def test_configuration(self):
        """Test configuration handling."""
        config = ComponentNameConfig()
        config.setting_1 = test_value
        component = ComponentName(config)
        assert component.config.setting_1 == test_value
    
    def test_signals(self):
        """Test signal emission."""
        component = ComponentName()
        signal_received = False
        
        def handler():
            nonlocal signal_received
            signal_received = True
        
        component.signal_name.connect(handler)
        # Trigger signal emission
        assert signal_received
```

### **Phase 5: Integration** (15 minutes)
```python
# __init__.py
from .component_name import ComponentName
from .config import ComponentNameConfig

__all__ = ['ComponentName', 'ComponentNameConfig']
```

---

## Quality Assurance Phase

### **Code Review Checklist** (10 minutes)
- [ ] **Naming conventions** followed (PascalCase classes, snake_case files)
- [ ] **Inheritance correct** (appropriate base classes used)
- [ ] **Configuration complete** (all settings configurable)
- [ ] **Signals defined** (appropriate events emitted)
- [ ] **Documentation present** (docstrings and comments)
- [ ] **Error handling** (validation and error cases)

### **Integration Testing** (15 minutes)
- [ ] **Import works** from other modules
- [ ] **Instantiation successful** with default and custom config
- [ ] **Signals function** correctly
- [ ] **Styling applies** properly
- [ ] **Performance acceptable** for intended use

### **Documentation Update** (10 minutes)
- [ ] **Component registry** updated with new component
- [ ] **Architecture docs** updated if new patterns introduced
- [ ] **Usage examples** created for other developers
- [ ] **Integration guide** updated if needed

---

## Component Migration (For Existing Components)

### **Assessment Phase** (15 minutes)
```markdown
## Migration Assessment

### Current State
**Location**: [Current file location]
**Structure**: [How it's currently organized]
**Dependencies**: [What it depends on]
**Usage**: [Where it's used in codebase]

### Target State
**New Location**: [Where it should go based on protocol]
**Required Changes**: [What needs to be modified]
**Breaking Changes**: [What might break]
**Migration Steps**: [Step-by-step plan]
```

### **Migration Execution**
1. **Create new structure** following protocol
2. **Copy and refactor** existing code
3. **Update imports** throughout codebase
4. **Test thoroughly** to ensure nothing broke
5. **Archive old version** in z_archive folder

---

## Integration with Session Protocol

### **Session Documentation**
```markdown
## Component Creation Session

### Component Details
**Name**: [ComponentName]
**Type**: [Base Class | Reusable Component | Utility]
**Location**: [File path]
**Purpose**: [What it does]

### Design Decisions
**Base Class**: [What it inherits from and why]
**Configuration**: [Key configurable aspects]
**Signals**: [Events it emits and why]
**Integration**: [How it works with existing code]

### Testing Results
**Unit Tests**: [Pass/Fail status]
**Integration Tests**: [Results]
**Performance**: [Acceptable/Issues noted]
```

---

## Agent-Specific Optimizations

### **For Windsurf**
- Use file templates for quick component creation
- Leverage integrated testing tools
- Use refactoring tools for safe renames

### **For Augment**
- Use codebase retrieval for pattern analysis
- Leverage context engine for similar components
- Use task management for complex components

### **For Kilo**
- Use collaborative features for design review
- Share component designs with team
- Coordinate integration efforts

---

## Success Criteria

### **Component Creation Complete When**:
- [ ] **Component follows protocol** (naming, structure, patterns)
- [ ] **Configuration system** implemented and tested
- [ ] **All tests pass** (unit and integration)
- [ ] **Documentation complete** (code and usage docs)
- [ ] **Integration verified** (works with existing code)
- [ ] **Performance acceptable** for intended use
- [ ] **Architecture docs updated** if new patterns introduced

### **Quality Indicators**:
- [ ] **Consistent with existing patterns** in codebase
- [ ] **Easy to use** by other developers
- [ ] **Well documented** with clear examples
- [ ] **Properly tested** with good coverage
- [ ] **Maintainable** with clear structure

---

**This workflow ensures GUI components follow industry standards and integrate seamlessly with the existing codebase.**
