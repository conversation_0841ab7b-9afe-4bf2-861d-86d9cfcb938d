# Mermaid 8.8 Syntax Reference & Compatibility Guide

**Version**: Mermaid 8.8  
**Date**: 2025-07-21  
**Purpose**: Quick reference for Mermaid 8.8 compatible syntax, especially for flowcharts

---

## 🚨 Critical Compatibility Issues (Mermaid 8.8)

### Special Characters That Break Syntax
| Character | Issue | Solution |
|-----------|--------|----------|
| `#` | Hash symbol breaks parsing | Use `#35;` or wrap in quotes |
| `end` | Reserved keyword | Wrap in quotes: `"end"` |
| `{}` | Confuses renderer | Avoid in comments, wrap labels in quotes |
| `✓` `☐` | Unicode symbols may fail | Use text alternatives or HTML entities |
| `<br>` | Line breaks need proper format | Use `<br/>` or `\n` in quoted strings |

### Node Label Best Practices
```mermaid
flowchart TD
    %% WRONG - will break in 8.8
    A[Node with # hash] --> B[Node with end word]
    
    %% CORRECT - 8.8 compatible
    A["Node with # hash"] --> B["Node with end word"]
    C["Node with ✓ checkmark"] --> D["Node with <br/> line break"]
```

---

## ✅ Flowchart Syntax (Mermaid 8.8 Safe)

### Basic Structure
```mermaid
flowchart [direction]
    nodeId["Label text"]
    nodeId --> nextNode
```

**Directions:**
- `TB` or `TD` - Top to bottom
- `BT` - Bottom to top  
- `LR` - Left to right
- `RL` - Right to left

### Safe Node Definitions
```mermaid
flowchart TD
    %% Safe node formats
    A["Square Rectangle"]
    B["Rounded Rectangle"]
    C(("Circle"))
    D{"Diamond/Rhombus"}
    E>Odd shape]
    F("Double Circle")
```

### Link Types (8.8 Compatible)
```mermaid
flowchart LR
    A --> B           %% Arrow
    A --- C           %% Line
    A -.-> D          %% Dotted
    A ==> E           %% Thick
    A -- text --> F   %% With text
    A -. text .-> G   %% Dotted with text
    A == text ==> H   %% Thick with text
```

---

## 🔧 Subgraph Syntax (Fixed for 8.8)

### Correct Subgraph Structure
```mermaid
flowchart TD
    subgraph Subgraph_Title["Subgraph Label"]
        direction LR
        A["Node A"] --> B["Node B"]
        C["Node C"] --> D["Node D"]
    end
    
    %% Connect to subgraph nodes
    E["External"] --> A
```

### Common Subgraph Issues & Fixes
| Issue | Problem | 8.8 Solution |
|-------|---------|--------------|
| `subgraph Title[Label]` | Wrong syntax | `subgraph Title["Label"]` |
| Missing `end` | Syntax error | Always close with `end` |
| Special chars in title | Breaks parsing | Use quotes: `subgraph "Title with #"` |

---

## 🎯 Working Examples for Your Use Case

### Flexible Database Update Flowchart (8.8 Safe)
```mermaid
flowchart TD
    subgraph Database_Mode["Database Mode"]
        direction LR
        Start["App Starts"] --> Config["Configure Settings"]
        Config --> Source["Select Source<br/>(Any folder)"]
        Source --> Check["Check for Master CSV"]
        Check -->|Exists| Update["Update Master CSV"]
        Check -->|Not exists| Create["Create new output"]
        Update --> Process["Process Files"]
        Create --> Process
        Process --> Result["Show Results"]
    end
```

### File Utility Mode (8.8 Safe)
```mermaid
flowchart TD
    subgraph File_Utility["File Utility Mode"]
        direction LR
        Start["Start"] --> Source["Select Source Files<br/>(Any location)"]
        Source --> Dest["Choose Save Location"]
        Dest --> Options["Options:<br/>- New file<br/>- Update existing<br/>- Master CSV"]
        Options --> Process["Process Files"]
        Process --> Result["Results"]
    end
```

---

## 📝 Quick Fix Checklist

When getting syntax errors:

1. **Wrap all labels in quotes**: `"Label text"` instead of `[Label text]`
2. **Avoid special characters**: Use text alternatives
3. **Check subgraph syntax**: Use `subgraph Title["Label"]` format
4. **Use HTML entities**: `&check;` instead of `✓`, `&square;` instead of `☐`
5. **Test incrementally**: Build graph step by step

### Character Replacements
| Original | Replacement | HTML Entity |
|----------|-------------|-------------|
| ✓ | check | `&check;` |
| ☐ | unchecked | `&square;` |
| # | hash | `&#35;` |
| <br> | line break | `<br/>` |

---

## 🧪 Testing Your Graphs

### Minimal Test Case
```mermaid
flowchart TD
    A["Start"] --> B["Process"]
    B --> C["End"]
```

### Add Complexity Gradually
1. Start with basic nodes and arrows
2. Add subgraphs one at a time
3. Test special characters last
4. Use quotes for all labels

---

## 📚 Additional Resources

- **Mermaid Live Editor**: https://mermaid.live
- **Official Docs**: https://mermaid-js.github.io/mermaid/
- **Version Compatibility**: Check renderer version with `mermaid.version` in browser console