# PDF Parsing Package Requirements
# Based on packages mentioned in pdf_parsing_workflow.md

# Core PDF parsing packages
camelot-py[cv]>=0.10.1    # Table extraction with lattice and stream flavors
pdfplumber>=0.9.0         # Complex text and table extraction
PyPDF2>=3.0.0            # Basic PDF text extraction (baseline comparison)

# Supporting packages
pandas>=1.5.0            # Data manipulation and analysis
reportlab>=4.0.0         # For creating sample PDFs in tests

# Optional dependencies for enhanced functionality
opencv-python>=4.8.0     # Required for camelot lattice flavor
ghostscript              # Required for camelot (system dependency)

# Development and testing
pytest>=7.0.0           # For running tests
jupyter>=1.0.0          # For prototyping phase (as mentioned in workflow)
