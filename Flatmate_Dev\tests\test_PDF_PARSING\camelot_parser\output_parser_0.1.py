#!/usr/bin/env python3
"""
Output Parser v0.1 - Stage 2 of PDF Processing Pipeline

Processes structured JSON from PDF Parser into clean transaction records.
Consolidates fragmented rows and applies account metadata.
"""

import json
import pandas as pd
import argparse
import os
import re
from datetime import datetime

def to_numeric(value_str):
    """Convert string to numeric, handling currency symbols."""
    if not value_str or pd.isna(value_str):
        return None
    
    # Remove currency symbols and whitespace
    clean_str = re.sub(r'[$,]', '', str(value_str)).strip()
    
    try:
        return float(clean_str) if clean_str else None
    except (ValueError, TypeError):
        return None

def consolidate_transaction_rows(raw_transactions):
    """Consolidate master and continuation rows into complete transactions."""
    consolidated = []
    current_transaction = None
    
    for row in raw_transactions:
        row_data = row['row_data']
        row_type = row['row_type']
        
        if row_type == 'master':
            # Save previous transaction if exists
            if current_transaction is not None:
                consolidated.append(current_transaction)
            
            # Start new transaction
            current_transaction = {
                'date': row_data[0] if len(row_data) > 0 else '',
                'description': row_data[1] if len(row_data) > 1 else '',
                'withdrawals': to_numeric(row_data[2]) if len(row_data) > 2 else None,
                'deposits': to_numeric(row_data[3]) if len(row_data) > 3 else None,
                'balance': to_numeric(row_data[4]) if len(row_data) > 4 else None,
                'table_number': row['table_number'],
                'source_rows': [row['row_index']]
            }
        
        elif row_type == 'continuation' and current_transaction is not None:
            # Append continuation description
            continuation_desc = row_data[1] if len(row_data) > 1 else ''
            if continuation_desc.strip():
                current_transaction['description'] += f" {continuation_desc.strip()}"
            
            current_transaction['source_rows'].append(row['row_index'])
    
    # Don't forget the last transaction
    if current_transaction is not None:
        consolidated.append(current_transaction)
    
    return consolidated

def process_structured_data(input_path, output_path):
    """Process structured JSON into clean CSV transactions."""
    print(f"Processing structured data from {input_path}...")
    
    # Extract source file name from the input path
    source_file = os.path.basename(input_path)
    
    # Load structured data
    try:
        with open(input_path, 'r') as f:
            data = json.load(f)
    except Exception as e:
        print(f"Error loading structured data: {e}")
        return
    
    all_transactions = []
    all_accounts = []
    
    for account in data['accounts']:
        account_metadata = account['account_metadata']
        raw_transactions = account['raw_transactions']
        
        # Store account metadata for test summary
        all_accounts.append(account_metadata)
        
        print(f"Processing account: {account_metadata.get('account_number', 'Unknown')}")
        
        # Consolidate fragmented transaction rows
        consolidated_transactions = consolidate_transaction_rows(raw_transactions)
        
        print(f"  Consolidated {len(raw_transactions)} raw rows into {len(consolidated_transactions)} transactions")
        
        # Apply account metadata to each transaction
        for transaction in consolidated_transactions:
            # Create standardised transaction record
            standardised_transaction = {
                'account_number': account_metadata.get('account_number', ''),
                'account_name': account_metadata.get('account_name', ''),
                'product_name': account_metadata.get('product_name', ''),
                'personalised_name': account_metadata.get('personalised_name', ''),
                'statement_period': account_metadata.get('statement_period', ''),
                'date': transaction['date'],
                'description': transaction['description'],
                'withdrawals': transaction['withdrawals'],
                'deposits': transaction['deposits'],
                'balance': transaction['balance'],
                'table_number': transaction['table_number'],
                'source_rows': ','.join(map(str, transaction['source_rows'])),
                'source_file': source_file,
                'processing_timestamp': datetime.now().isoformat()
            }
            
            all_transactions.append(standardised_transaction)
    
    if not all_transactions:
        print("No transactions found to process")
        return
    
    # Create DataFrame and save as CSV
    df = pd.DataFrame(all_transactions)
    
    # Ensure output directory exists
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    # Save to CSV
    df.to_csv(output_path, index=False)
    
    print(f"Saved {len(all_transactions)} standardised transactions to {output_path}")
    
    # Print summary statistics
    print("\nProcessing Summary:")
    print(f"  Total transactions: {len(all_transactions)}")
    print(f"  Unique accounts: {df['account_number'].nunique()}")
    print(f"  Date range: {df['date'].min()} to {df['date'].max()}" if not df.empty else "  No date range available")
    print(f"  Total withdrawals: ${df['withdrawals'].sum():.2f}" if df['withdrawals'].notna().any() else "  No withdrawals")
    print(f"  Total deposits: ${df['deposits'].sum():.2f}" if df['deposits'].notna().any() else "  No deposits")
    
    # Add test metadata at the end of the file
    with open(output_path.replace('.csv', '_test_metadata.txt'), 'w') as f:
        f.write("=== TEST METADATA ===\n")
        f.write(f"Source file: {source_file}\n")
        f.write(f"Processing timestamp: {datetime.now().isoformat()}\n")
        f.write(f"Total accounts found: {len(all_accounts)}\n")
        f.write(f"Total transactions processed: {len(all_transactions)}\n\n")
        
        f.write("=== ACCOUNT SUMMARY ===\n")
        for i, account in enumerate(all_accounts, 1):
            f.write(f"Account {i}:\n")
            f.write(f"  Account Number: {account.get('account_number', 'Unknown')}\n")
            f.write(f"  Account Name: {account.get('account_name', 'Unknown')}\n")
            f.write(f"  Product Name: {account.get('product_name', 'Unknown')}\n")
            f.write(f"  Personalised Name: {account.get('personalised_name', 'Unknown')}\n")
            f.write(f"  Statement Period: {account.get('statement_period', 'Unknown')}\n")
            
            # Count transactions for this account
            if not df.empty:
                account_txns = df[df['account_number'] == account.get('account_number', '')]
                f.write(f"  Transactions: {len(account_txns)}\n")
            else:
                f.write("  Transactions: 0\n")
            f.write("\n")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description='Process structured JSON into clean transaction CSV (Stage 2 of pipeline)'
    )
    parser.add_argument(
        'input_file',
        type=str,
        help='Path to structured JSON file from Stage 1'
    )
    parser.add_argument(
        'output_file',
        type=str,
        help='Path for clean transaction CSV output'
    )
    
    args = parser.parse_args()
    process_structured_data(args.input_file, args.output_file)
