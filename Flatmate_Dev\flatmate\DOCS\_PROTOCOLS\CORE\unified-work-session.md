---
description: "Unified work session protocol - master workflow for all development work"
type: "core_workflow"
version: "1.0"
agents: ["windsurf", "augment", "kilo", "claude", "any"]
---

# Unified Work Session Workflow

**Purpose**: Master workflow for all development work sessions  
**Supports**: FEATURE | REFACTOR | TROUBLESHOOT | MAINTENANCE  
**Integration**: Works with all AI agents and development tools  

---

## Quick Start

### 1. **Session Classification** (30 seconds)
```
What type of work am I doing?
├─ Building new functionality → FEATURE
├─ Improving existing code → REFACTOR  
├─ Fixing bugs/issues → TROUBLESHOOT
└─ Updates/cleanup → MAINTENANCE
```

### 2. **Session Setup** (2 minutes)
```bash
# Create session folder
mkdir "flatmate/DOCS/_FEATURES/<SESSION_TYPE>_<descriptive_name>"
cd "flatmate/DOCS/_FEATURES/<SESSION_TYPE>_<descriptive_name>"

# Copy session guide
cp "../../_PROTOCOLS/GUIDES/session_documentation_guide.md" "SESSION_LOG.md"

# Create evidence folder
mkdir -p "EVIDENCE/{error_logs,screenshots,code_samples}"
```

### 3. **Initialize Session Log**
- Open `SESSION_LOG.md`
- Fill in header (date, type, objective, success criteria)
- Begin real-time logging

---

## Session Execution (Type-Specific)

### **FEATURE Sessions**
**Follow**: Feature Protocol v1.1
1. **Requirements Analysis** - No speculation, use codebase retrieval
2. **Architecture Analysis** - Understand existing patterns
3. **Task Breakdown** - Atomic tasks with complete code examples
4. **Implementation** - With testing at each step
5. **User Review** - Get feedback before completion

### **REFACTOR Sessions**
**Process**:
1. **Document Current State** - What exists now?
2. **Define Target State** - What should it become?
3. **Create Refactor Plan** - Step-by-step approach
4. **Execute Changes** - With testing at each step
5. **Verify Results** - Ensure nothing broke

### **TROUBLESHOOT Sessions**
**Follow**: Enhanced Troubleshooting Protocol
1. **Clarify Problem** - Actual vs expected behavior
2. **Gather Evidence** - Logs, errors, stack traces
3. **Isolate Issue** - Minimal reproduction
4. **Hypothesize Causes** - Root causes, not symptoms
5. **Test Hypotheses** - Minimal, reversible changes
6. **Implement Solution** - Clean, tested fix
7. **Verify Resolution** - Confirm fix works

### **MAINTENANCE Sessions**
**Process**:
1. **Identify Scope** - What needs maintenance?
2. **Plan Approach** - Order of operations
3. **Execute Tasks** - With verification
4. **Update Documentation** - Reflect changes

---

## Real-Time Documentation

### **Continuous Logging** (Every 15-30 minutes)
```markdown
### [HH:MM] [Phase/Action Name]
- **Action**: [What was done]
- **Discovery**: [What was learned/found]
- **Decision**: [Any choices made]
- **Next**: [What to do next]
```

### **Issue Tracking** (When problems occur)
```markdown
### [HH:MM] Issue Encountered
- **Problem**: [Clear description]
- **Attempted Solution**: [What was tried]
- **Result**: [What happened]
- **Resolution**: [How it was solved]
- **Lesson**: [What we learned]
```

### **Evidence Collection** (Save immediately)
- **Error logs** → `EVIDENCE/error_logs/`
- **Screenshots** → `EVIDENCE/screenshots/`
- **Code samples** → `EVIDENCE/code_samples/`

---

## Session Completion (MANDATORY)

### **Complete Lessons Learned** (5 minutes)
```markdown
## Lessons Learned

### What Worked Well
- **Process**: [Effective workflow/approach]
- **Technical**: [Successful code pattern/tool]
- **AI Collaboration**: [Effective interaction pattern]
- **Time Management**: [Productivity insight]

### What Didn't Work
- **Process**: [Workflow friction/waste]
- **Technical**: [Failed approach/inefficiency]
- **AI Collaboration**: [Confusing interaction]
- **Time Management**: [Estimation error/blocker]

### Key Insights Gained
- **Architecture**: [System design understanding]
- **Codebase**: [Pattern recognition/understanding]
- **Tools**: [New capability/better usage]
- **Problem-Solving**: [Effective technique]

### Process Improvements Identified
- **Immediate**: [Apply right now]
- **Template Updates**: [Documentation improvements]
- **Workflow Enhancements**: [Process changes]
- **Tool/Automation**: [Automation opportunities]

### For Next Session
- **Apply Immediately**: [Use in next session]
- **Remember**: [Important context]
- **Avoid**: [Mistakes not to repeat]
- **Try**: [New approaches to experiment with]
```

### **Create Session Changelog** (5 minutes)
**Use template from**: `flatmate/DOCS/_PROTOCOLS/GUIDES/project_documentation_guide.md`

**Include**:
- Session summary and accomplishments
- All files modified with descriptions
- Testing results and verification
- Architecture benefits and decisions
- Technical debt identified
- Future enhancement opportunities

### **Apply Immediate Improvements** (2 minutes)
- Fix obvious template issues
- Update snippets/shortcuts for next session
- Note systemic issues for weekly review

### **Archive and Complete** (3 minutes)
- Move evidence files to EVIDENCE/ folder
- Update session status (COMPLETE/ON_HOLD/NEEDS_FOLLOWUP)
- Set clear next steps
- Mark session end time

---

## Integration Points

### **With AI Agents**
- **Context Sharing**: Provide session folder path for context
- **Protocol Selection**: Agent selects appropriate sub-protocol
- **Documentation**: Agent helps maintain real-time logs
- **Quality Assurance**: Agent validates protocol compliance

### **With Development Tools**
- **VS Code**: Use snippets for quick logging
- **Git**: Include session reference in commit messages
- **Testing**: Integrate testing at each step
- **Debugging**: Systematic evidence collection

### **With Quality Systems**
- **Code Review**: Protocol compliance checked
- **Documentation**: Session logs provide context
- **Testing**: Verification requirements built-in
- **Continuous Improvement**: Lessons feed back to protocols

---

## Success Criteria

### **Every Session Must Have**:
- [ ] **Complete SESSION_LOG.md** with real-time documentation
- [ ] **Lessons learned section** fully completed
- [ ] **CHANGELOG.md** following documentation protocol
- [ ] **All files modified** documented with reasons
- [ ] **Testing results** recorded and verified
- [ ] **Technical debt** identified and recorded
- [ ] **Next steps** clearly defined and actionable

### **Quality Indicators**:
- [ ] Another developer can understand what happened
- [ ] All decisions have documented rationale
- [ ] Current state is clearly described
- [ ] Evidence is properly archived
- [ ] Immediate improvements were applied

---

## Agent-Specific Notes

### **For Windsurf**
- Use integrated terminal for session setup
- Leverage file explorer for evidence organization
- Use built-in git integration for commits

### **For Augment**
- Utilize codebase retrieval for architecture analysis
- Use context engine for understanding existing patterns
- Leverage task management for complex sessions

### **For Kilo**
- Use collaborative features for user review
- Integrate with project management tools
- Leverage team communication features

### **For Any Agent**
- Follow this workflow regardless of specific agent
- Adapt agent-specific features to workflow steps
- Maintain consistent documentation standards

---

## Troubleshooting

### **Session Setup Issues**
- **Folder creation fails**: Check permissions and path
- **Template not found**: Verify template location
- **Git issues**: Ensure repository is clean

### **Documentation Issues**
- **Logging feels overwhelming**: Start with major actions only
- **Templates unclear**: Update immediately for next session
- **Evidence collection missed**: Set reminders every 30 minutes

### **Protocol Compliance**
- **Steps skipped**: Use checklist at session end
- **Quality issues**: Review and improve templates
- **Time overruns**: Break into smaller sessions

---

**This workflow ensures consistent, high-quality development work with comprehensive documentation and continuous improvement, regardless of which AI agent you're using.**
