"""
Configuration Factory

Factory for creating widget configurations with type safety and extensibility.
"""

from typing import Type, TypeVar
from .widget_config import (
    BaseWidgetConfig, ButtonConfig, CheckBoxConfig, 
    LabelConfig, OptionMenuConfig, SelectorConfig, FilterConfig
)

T = TypeVar('T', bound=BaseWidgetConfig)


class ConfigFactory:
    """Factory for creating widget configurations."""
    
    _config_map = {
        'button': ButtonConfig,
        'checkbox': CheckBoxConfig,
        'label': LabelConfig,
        'option_menu': OptionMenuConfig,
        'selector': SelectorConfig,
        'filter': FilterConfig,
    }
    
    @classmethod
    def create_config(cls, widget_type: str, **kwargs) -> BaseWidgetConfig:
        """Create configuration for widget type.
        
        Args:
            widget_type: Type of widget ('button', 'checkbox', etc.)
            **kwargs: Configuration parameters
            
        Returns:
            Configured widget config instance
        """
        config_class = cls._config_map.get(widget_type, BaseWidgetConfig)
        return config_class(**kwargs)
    
    @classmethod
    def register_config(cls, widget_type: str, config_class: Type[T]):
        """Register new configuration type.
        
        Args:
            widget_type: Type identifier for the widget
            config_class: Configuration class to register
        """
        cls._config_map[widget_type] = config_class
    
    @classmethod
    def get_available_types(cls) -> list:
        """Get list of available widget types."""
        return list(cls._config_map.keys())
