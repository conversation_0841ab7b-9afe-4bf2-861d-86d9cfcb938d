#!/usr/bin/env python3
"""
PDF Parsing Pipeline Main Entry Point v0.1

Combines both stages of the PDF parsing pipeline:
1. PDF Parser: Extracts tables from PDF and identifies account boundaries
2. Output Parser: Processes structured JSON into clean transaction records

Usage:
    python main.py input.pdf output.csv [--intermediate structured_data.json]
"""

import os
import argparse
import tempfile
import importlib.util
from datetime import datetime

# Import functions from both pipeline stages using importlib
# This handles module names with dots in them
def import_module_from_file(module_name, file_path):
    spec = importlib.util.spec_from_file_location(module_name, file_path)
    module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(module)
    return module

# Get the directory of this script
current_dir = os.path.dirname(os.path.abspath(__file__))

# Import the parser modules
pdf_parser = import_module_from_file("pdf_parser", os.path.join(current_dir, "pdf_parser_0.1.py"))
output_parser = import_module_from_file("output_parser", os.path.join(current_dir, "output_parser_0.1.py"))

def run_pipeline(pdf_path, output_path, intermediate_path=None):
    """
    Run the complete PDF parsing pipeline.
    
    Args:
        pdf_path: Path to input PDF file
        output_path: Path for final CSV output
        intermediate_path: Optional path for intermediate JSON output
                          (if None, uses a temporary file)
    """
    print(f"=== PDF PARSING PIPELINE v0.1 ===")
    print(f"Started at: {datetime.now().isoformat()}")
    print(f"Input PDF: {pdf_path}")
    print(f"Output CSV: {output_path}")
    
    # Create intermediate file path if not provided
    using_temp = False
    if not intermediate_path:
        using_temp = True
        fd, intermediate_path = tempfile.mkstemp(suffix='.json', prefix='structured_data_')
        os.close(fd)
        print(f"Using temporary intermediate file: {intermediate_path}")
    else:
        print(f"Intermediate JSON: {intermediate_path}")
    
    try:
        # Stage 1: Extract tables from PDF
        print("\n=== STAGE 1: PDF PARSING ===")
        pdf_parser.extract_pdf_tables(pdf_path, intermediate_path)
        
        # Stage 2: Process structured data
        print("\n=== STAGE 2: OUTPUT PROCESSING ===")
        output_parser.process_structured_data(intermediate_path, output_path)
        
        print(f"\n=== PIPELINE COMPLETE ===")
        print(f"Final output saved to: {output_path}")
        print(f"Test metadata saved to: {output_path.replace('.csv', '_test_metadata.txt')}")
        print(f"Completed at: {datetime.now().isoformat()}")
        
    finally:
        # Clean up temporary file if used
        if using_temp and os.path.exists(intermediate_path):
            os.remove(intermediate_path)
            print(f"Removed temporary intermediate file")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description='PDF Parsing Pipeline - Extract transactions from PDF bank statements'
    )
    parser.add_argument(
        'pdf_file',
        type=str,
        help='Path to input PDF file'
    )
    parser.add_argument(
        'output_file',
        type=str,
        help='Path for final transaction CSV output'
    )
    parser.add_argument(
        '--intermediate',
        type=str,
        help='Optional path for intermediate structured JSON output',
        default=None
    )
    
    args = parser.parse_args()
    run_pipeline(args.pdf_file, args.output_file, args.intermediate)
