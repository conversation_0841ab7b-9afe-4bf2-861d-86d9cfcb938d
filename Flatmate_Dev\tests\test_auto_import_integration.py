#!/usr/bin/env python3
"""
Integration test for auto-import UI integration with update_data module.

Tests that the "Set auto import folder..." option is properly integrated
into the update_data module's source selection combo box.
"""

import os
import sys
import unittest
from unittest.mock import Mock, patch

# Add the flatmate source directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'flatmate', 'src'))

from fm.modules.update_data.utils.option_types import SourceOptions


class TestAutoImportIntegration(unittest.TestCase):
    """Test cases for auto-import UI integration"""
    
    def test_source_options_includes_auto_import(self):
        """Test that SourceOptions includes the auto-import option"""
        # Check that the new option exists
        self.assertTrue(hasattr(SourceOptions, 'SET_AUTO_IMPORT'))
        
        # Check the value is correct
        self.assertEqual(SourceOptions.SET_AUTO_IMPORT.value, "Set auto import folder...")
        
        # Check all expected options are present
        expected_options = [
            "Select entire folder...",
            "Select individual files...", 
            "Set auto import folder..."
        ]
        
        actual_options = [option.value for option in SourceOptions]
        self.assertEqual(set(expected_options), set(actual_options))
    
    def test_source_options_enum_values(self):
        """Test that all SourceOptions enum values are strings"""
        for option in SourceOptions:
            self.assertIsInstance(option.value, str)
            self.assertTrue(len(option.value) > 0)
    
    @patch('fm.modules.update_data.ud_presenter.configure_auto_import')
    def test_presenter_handles_auto_import_option(self, mock_configure):
        """Test that the presenter properly handles the auto-import option"""
        # This test would require more complex setup to test the full presenter
        # For now, we just verify the import works
        from fm.modules.update_data.ud_presenter import UpdateDataPresenter
        
        # Verify the import was successful (configure_auto_import is imported)
        self.assertTrue(hasattr(sys.modules['fm.modules.update_data.ud_presenter'], 'configure_auto_import'))
    
    def test_dialog_utils_import(self):
        """Test that dialog utils can be imported successfully"""
        try:
            from fm.gui._shared_components.utils.dialog_utils import configure_auto_import
            # If we get here, the import was successful
            self.assertTrue(callable(configure_auto_import))
        except ImportError as e:
            self.fail(f"Failed to import configure_auto_import: {e}")


if __name__ == '__main__':
    unittest.main()
