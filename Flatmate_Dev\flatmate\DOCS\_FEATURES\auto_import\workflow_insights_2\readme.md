# Workflow Insights

A folder for insights and proposed new or updated workflow protocols informed by insights uncovered during each work session.

## Purpose
This folder captures workflow improvements, protocol refinements, and process insights discovered during development sessions. It serves as a knowledge base for evolving our development practices.

## Contents

### Protocols
- `end_of_chat_session_protocol.md` - Quick context preservation for individual AI chat sessions (5-10 min)
- `end_of_sprint_protocol.md` - Comprehensive workflow for completing development sprints (15-20 min)
- `post_sprint_review_<n>.md` - Sprint review documents (format: post_sprint_review_1.md)

### Insights Categories
- **Process Improvements**: Better ways to structure development work
- **Documentation Workflows**: More effective documentation practices
- **Quality Gates**: Checkpoints to ensure work quality
- **Session Management**: Individual chat session context preservation
- **Sprint Planning**: Strategic sprint setup and milestone planning
- **User Feedback Integration**: Better ways to incorporate user testing

## Workflow Integration
These insights integrate with:
- `.augment/rules/update-docs.md` - Documentation workflow
- `.kilocode/rules/implementation-protocol.md` - Implementation protocol
- `DOCS/_ARCHITECTURE/` - Architecture documentation
- `DOCS/_FEATURES/` - Feature-specific documentation

## Usage
1. **During Chat Sessions**: Note workflow pain points or improvements
2. **End of Chat Session**: Use quick protocol for context preservation (5-10 min)
3. **End of Sprint**: Use comprehensive protocol for strategic planning (15-20 min)
4. **Between Sessions**: Review insights to improve next session
5. **Protocol Updates**: Evolve protocols based on accumulated insights

## File Naming Conventions
- **Protocols**: `<protocol_name>_protocol.md`
- **Session Notes**: `session_notes_<YYMMDD>.md` (e.g., `session_notes_250122.md`)
- **Sprint Reviews**: `post_sprint_review_<n>.md` (e.g., `post_sprint_review_1.md`)
- **Insights**: `<topic>_insights.md`
- **Process Updates**: `<process_name>_v<version>.md`

---

**Goal**: Continuously improve development workflows through systematic capture and application of insights.