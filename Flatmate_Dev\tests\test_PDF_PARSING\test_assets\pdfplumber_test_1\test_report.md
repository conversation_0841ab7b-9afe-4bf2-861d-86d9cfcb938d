# pdfplumber PDF Parser Test Report

## Test Information
- **Test Date:** 2025-07-21
- **Test Time:** 12:29:08 NZST
- **Package:** pdfplumber 0.7.6
- **Extraction Methods:** extract_text, extract_words, extract_tables, chars, lines, rects
- **File Tested:** `tests/test_pdfs/kiwibank/2024-Dec-23_Personal.pdf`

## Test Configuration
```python
with pdfplumber.open(pdf_path) as pdf:
    for page in pdf.pages:
        text = page.extract_text()
        words = page.extract_words()
        tables = page.extract_tables()
        chars = page.chars
        lines = page.lines
        rects = page.rects
```

## Test Results
- **Tables Found:** 0 (No tables detected on any page)
- **Pages Processed:** 6 (All pages in the PDF)
- **Text Extraction:** Successful on all pages
- **Word Extraction:** Successful on all pages
- **Character Data:** Successful on all pages
- **Lines and Rectangles:** Detected on all pages

## Data Extraction Summary
| Page | Words | Characters | Lines | Rectangles | Tables |
|------|-------|------------|-------|------------|--------|
| 1    | 124   | 882        | 12    | 1          | 0      |
| 2    | 357   | 1903       | 74    | 0          | 0      |
| 3    | 365   | 1927       | 72    | 0          | 0      |
| 4    | 334   | 1792       | 68    | 0          | 0      |
| 5    | 300   | 1637       | 49    | 0          | 0      |
| 6    | 95    | 619        | 4     | 0          | 0      |
| Total| 1575  | 8760       | 279   | 1          | 0      |

## Files Generated
- `pdfplumber_text_page_[1-6].txt` - Extracted text for each page
- `pdfplumber_words_page_[1-6].json` - Words with positioning data for each page
- `pdfplumber_chars_page_[1-6].json` - Character data with metadata for each page
- `pdfplumber_lines_page_[1-6].json` - Line data for each page
- `pdfplumber_rects_page_[1-6].json` - Rectangle data for each page
- `pdfplumber_metadata.json` - PDF metadata

## Notes
- pdfplumber successfully extracted text from all pages
- No tables were detected using the default `extract_tables()` method
- Character-level data with positioning is available and could be used for custom table extraction
- Word-level data with positioning could be used to identify account numbers, balances, etc.
- The PDF contains 279 lines (graphical elements) that could be used to identify table structures

## Comparison with Camelot
- pdfplumber did not detect any tables with its default method, while Camelot's stream method found 10 tables
- pdfplumber provides more detailed character and word positioning data
- pdfplumber extracts graphical elements (lines, rectangles) that could be used for custom table detection
- pdfplumber's text extraction is more comprehensive (8760 characters vs Camelot's focus on tables)

## Next Steps
- Explore custom table extraction using pdfplumber's character and line data
- Consider using pdfplumber for text extraction and metadata, and Camelot for table extraction
- Analyze the word positioning data to identify patterns for account numbers and balances
# >> sounds like a mssive P I T A to be honest ..
>> as far as source handling goes - I think the database should favour csv or the upcoming ofx parsing 
- pdf parsing seems less reliable 
- as far as implementation goes camelots tables are tricky enough the fact pdf plumber cant even dedect obvious tables is a bad sign 


