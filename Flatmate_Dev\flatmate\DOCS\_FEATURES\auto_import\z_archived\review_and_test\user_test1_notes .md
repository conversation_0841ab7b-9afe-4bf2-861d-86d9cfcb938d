

- (there is a double up on infobars ud_data is obviously still creating its old prototype vversion)

center panel has an early stand in welcome message and is otherwise blank

![1753023199149](image/25720_flatmate/1753023199149.png)


the gui implementation while approaching functional now, seems illogical in the flow for the user ....

The question is what should it do? what would make sense? we have an unused right panel 
which was intended for context specific options and settings

We have a huge mostly empty center panel

we need to think about some good gui design principles
at the moment the update database chek box is a hacked in place label and check box that doesnt use the app base widgets 

we have numerous optins we can show hide elements, swap them out 
put the table view in the center - such that switching modules simply means swapping out panels for the the viewer
which would be fine - making the nav bar / side bar simply a task context switxher as far as the user is concerned... we have  alot of room to play with the question is ux design and logical flow

for now the priority is probably to get the auto import logic working and some feed back in the center panel 

i should export these test notes to the feature appropriate auto import feature design dir 
But where exactly the raises a question about out workflow protocls foe this review debug and refine part of the new feature process 