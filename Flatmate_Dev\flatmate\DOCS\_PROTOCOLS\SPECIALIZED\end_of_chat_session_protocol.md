# End-of-Chat-Session Protocol

**Version**: 1.0
**Date**: 2025-01-22  
**Status**: ACTIVE  
**Purpose**: Quick context preservation for individual AI chat sessions

## 🎯 **Protocol Purpose**

Lightweight workflow for ending individual chat sessions while preserving technical context for continuation. Focuses on **immediate handover** rather than comprehensive review.

## ⚡ **Quick Protocol Checklist (5-10 minutes)**

### **Phase 1: Session Documentation (MANDATORY)**

#### ✅ **1.1 Update Feature Changelog**
**Location**: `DOCS/_FEATURES/<feature_name>/CHANGELOG.md`
**Time**: 3 minutes

**AI Actions**:
- [ ] Add session entry with date and brief summary
- [ ] List files modified with specific changes
- [ ] Note current status (working/broken/testing needed)
- [ ] Record any immediate blockers or issues

#### ✅ **1.2 Create Session Notes**
**Location**: `DOCS/_FEATURES/<feature_name>/session_notes_<YYMMDD>.md`
**Time**: 5 minutes

**AI Actions**:
- [ ] Document current technical state
- [ ] List immediate next actions (1-3 items max)
- [ ] Note any context needed for continuation
- [ ] Record any decisions made or approaches tried

#### ✅ **1.3 Update Outstanding Items (If Changed)**
**Location**: `DOCS/_FEATURES/<feature_name>/outstanding_items.md`
**Time**: 2 minutes

**AI Actions**:
- [ ] Add any new items discovered
- [ ] Mark completed items as done
- [ ] Update priorities if they changed
- [ ] Note any new blockers

## 📄 **Session Notes Template**

```markdown
# Session Notes - <Feature Name>

**Date**: <YYMMDD>
**Duration**: <X hours>
**AI Session**: <Brief identifier>

## What Was Done
- [ ] Task 1 - <Status: COMPLETE/IN_PROGRESS/BLOCKED>
- [ ] Task 2 - <Status: COMPLETE/IN_PROGRESS/BLOCKED>
- [ ] Task 3 - <Status: COMPLETE/IN_PROGRESS/BLOCKED>

## Current Technical State
### Working
- Component/feature 1
- Component/feature 2

### Broken/Needs Fix
- Issue 1 - <Brief description>
- Issue 2 - <Brief description>

### In Progress
- Work item 1 - <What's left to do>
- Work item 2 - <What's left to do>

## Immediate Next Actions
1. **Priority 1**: <Specific action> (Est: <time>)
2. **Priority 2**: <Specific action> (Est: <time>)
3. **Priority 3**: <Specific action> (Est: <time>)

## Context for Next Developer/AI
### Important Notes
- Note 1: <Critical technical context>
- Note 2: <Critical technical context>

### Approaches Tried
- Approach 1: <What was tried and result>
- Approach 2: <What was tried and result>

### Potential Pitfalls
- Pitfall 1: <What to watch out for>
- Pitfall 2: <What to watch out for>

## Files Modified This Session
- `path/to/file1.py` - <What changed>
- `path/to/file2.py` - <What changed>

## Testing Status
- [ ] Needs testing: <What to test>
- [ ] Tested and working: <What was verified>
- [ ] Known issues: <What's broken>

---
**Session Complete**: Ready for next developer/AI to continue
```

## 🚀 **Protocol Execution (AI Workflow)**

### **Session Wrap-Up (5 minutes)**
1. Quick changelog update
2. Create session notes with current state
3. Update outstanding items if needed

### **Quality Gates**
- [ ] Current state clearly documented
- [ ] Next actions are specific and actionable
- [ ] Technical context preserved
- [ ] Files modified are listed

## 📊 **Success Metrics**

### **Efficiency**
- Protocol completion in <10 minutes
- No technical context lost
- Clear handover for continuation

### **Clarity**
- Next developer/AI can continue immediately
- No time wasted figuring out current state
- Specific next actions identified

## 🔗 **Integration with Other Protocols**

### **When to Use Each Protocol**
- **End-of-Chat Session Protocol** (this): End of individual AI interactions with documentation focus
- **Chat Handover Protocol**: When transitioning between AI chats due to lag/performance
- **Implementation Review Protocol**: Mid-sprint technical reviews and planning
- **End-of-Sprint Protocol**: End of major work phases, after user testing, milestone completion

### **Protocol Relationships**
- **Session notes** feed into sprint reviews and implementation reviews
- **Multiple chat sessions** contribute to one sprint
- **Chat handovers** preserve immediate context for continuation
- **Implementation reviews** update documentation and plan next attempts

---

**This protocol ensures no work context is lost between individual chat sessions while keeping overhead minimal.**
