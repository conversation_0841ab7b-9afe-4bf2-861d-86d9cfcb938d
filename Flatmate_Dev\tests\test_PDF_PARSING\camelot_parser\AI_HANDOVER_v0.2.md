# FLATMATE PDF PARSING PIPELINE: AI HANDOVER v0.2

## 1. Objective
Convert multi-account PDF bank statements into standardised, metadata-rich CSV transaction records with full account and source traceability.

**Pipeline:**
- Stage 1: Extracts tables and account metadata from PDF to structured JSON
- Stage 2: Consolidates and standardises transactions, outputs CSV and test metadata

---

## 2. Key Files & Entry Point
- `pdf_parser_0.1.py`: PDF → structured JSON (extracts accounts, transactions, metadata)
- `output_parser_0.1.py`: JSON → CSV (consolidates, standardises, adds metadata)
- `main.py`: **Unified entry point** (runs both stages, CLI interface)
- `AI_HANDOVER.md`, `PIPELINE_DESIGN.md`: Context, design, and extension notes

---

## 3. Usage

### Run Full Pipeline (from camelot_parser directory):
```bash
python main.py <input.pdf> <output.csv> [--intermediate structured_data.json]
```
- If `--intermediate` not provided, uses a temp file for JSON

### Example:
```bash
python main.py ../../test_pdfs/kiwibank/2024-Dec-23_Personal.pdf output/final_transactions_v0.1.csv
```

---

## 4. Pipeline Logic

### Stage 1: PDF Parsing (`pdf_parser_0.1.py`)
- Extracts all tables using Camelot
- **Account header detection:** Robust regex & pattern matching for account name, number, product, personalised name, statement period
- **Deduplication:** Unique accounts only (by account number)
- **Transaction association:** Two-pass approach—identify accounts, then associate tables by account number
- **Output:** `accounts` list with `account_metadata` and `raw_transactions` (with row types: master, continuation, metadata)
- **Prints summary**: Accounts found, transaction counts

### Stage 2: Output Processing (`output_parser_0.1.py`)
- Loads structured JSON
- **Consolidates** fragmented transaction rows (master + continuation)
- **Standardises**: Adds metadata fields (`account_name`, `product_name`, `source_file`, etc.)
- **Outputs:**
  - CSV of all transactions (with full metadata)
  - `_test_metadata.txt` file: Account summary, transaction counts, processing timestamp

### Entry Point (`main.py`)
- Uses `importlib` to load pipeline stages (handles dots in filenames)
- CLI: `pdf_file`, `output_file`, `--intermediate`
- Runs both stages in sequence
- Handles temp file cleanup if needed

---

## 5. Output Structure

### CSV Columns:
- `account_number`, `account_name`, `product_name`, `personalised_name`, `statement_period`
- `date`, `description`, `withdrawals`, `deposits`, `balance`
- `table_number`, `source_rows`, `source_file`, `processing_timestamp`

### Test Metadata:
- All accounts found (even with zero transactions)
- Account details and transaction counts
- Source file and processing timestamp

---

## 6. Recent Fixes & Improvements

- **Account Name Extraction:** Improved pattern matching and fallback logic; now always filled if possible
- **Source File Column:** Now present and correct in all outputs
- **Account-Transaction Association:** Two-pass, robust by account number
- **All Accounts in Metadata:** Metadata file lists all accounts, even those with no transactions
- **Deduplication:** Only unique accounts in output
- **Unified Entry Point:** Added `main.py` for single-command pipeline execution
- **Import Handling:** Uses `importlib` to work around Python filename restrictions

---

## 7. Testing

- Use the provided main.py CLI as above
- Test outputs are validated for:
  - Correct account name and metadata on all transactions
  - Source file traceability
  - All accounts present in test metadata file

---

## 8. Extension Points

- Further improve account header detection (regex/fuzzy)
- Handle multi-table-per-account scenarios more gracefully
- Add error handling/reporting for malformed PDFs
- Support new banks/formats by adding new handler modules

---

## 9. Environment

- Python 3.x (use `flatmate/.venv_fm313/` virtualenv)
- Dependencies: pandas, camelot, argparse, standard library

---

## 10. AI-Optimised Notes

- All processing logic is explicit and modular; minimal error catching (fail fast)
- Code is UK spelling and concise, with clear section comments
- Logging is via project singleton `log` (not used in this pipeline, but available)
- All output is deterministic and testable
- Entry point is robust to module import issues

---

**This document is optimised for AI handover: all critical context, file structure, logic, and extension points are summarised.  
See code and markdown files for further details.**
