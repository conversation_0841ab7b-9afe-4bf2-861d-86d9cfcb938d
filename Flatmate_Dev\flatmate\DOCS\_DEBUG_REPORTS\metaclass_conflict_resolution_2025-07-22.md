# Debug Report: Metaclass Conflict Resolution
**Date**: 2025-07-22  
**Time**: 08:41 NZST  
**Issue Type**: Python Metaclass Conflict  
**Component**: GUI Widget Base Class  
**File**: `flatmate/src/fm/gui/_shared_components/widgets/base/base_widget.py`

## Problem Summary

**Error Message**:
```
TypeError: metaclass conflict: the metaclass of a derived class must be a (non-strict) subclass of the metaclasses of all its bases
```

**Root Cause**: The `BaseWidget` class was inheriting from both `QWidget` and `ABC`, creating incompatible metaclass requirements between:
- Qt's `pyqtWrapperType` (used by `QWidget`)
- Python's `ABCMeta` (used by `ABC`)

## Resolution Applied

### **What I Did**
I simplified the `BaseWidget` class by removing all abstract class complexity:

1. **Removed ABC inheritance** and custom metaclass entirely
2. **Replaced `@abstractmethod` decorators** with simple method implementations
3. **Provided sensible default implementations** instead of forcing subclasses to implement everything

### **Current Code Status**
The `BaseWidget` class now:
- **Inherits only from `QWidget`** (no metaclass conflicts)
- **Provides default implementations** for all methods
- **Uses simple method overrides** instead of abstract requirements
- **Maintains the same API** but without complexity

### **Specific Changes Made**
```python
# Before (problematic):
class BaseWidget(QWidget, ABC):
    @abstractmethod
    def _get_default_config(self) -> BaseWidgetConfig:
        pass

# After (simplified):
class BaseWidget(QWidget):
    def _get_default_config(self) -> BaseWidgetConfig:
        """Return default configuration for this widget type."""
        return BaseWidgetConfig()  # Simple default implementation
```

### **Current Implementation**
The class now provides:
- **Default configuration** via `BaseWidgetConfig()`
- **Empty UI setup** via `pass` statements
- **No-op content application** for subclasses to optionally override
- **Working signal emission** and method chaining

## Verification Results

```bash
$ cd flatmate && python -c "from fm.gui._shared_components.widgets.base.base_widget import BaseWidget; print('✅ Import successful')"
✅ Import successful
```

## Key Learning

**For simple widget base classes:**
- **Avoid ABC entirely** - it's overkill for basic widget inheritance
- **Use default implementations** instead of abstract methods
- **Let subclasses override** only what they need
- **Keep the base class concrete** and functional

The metaclass conflict is now fully resolved, and the simplified `BaseWidget` provides a clean foundation for GUI widget development without unnecessary complexity.