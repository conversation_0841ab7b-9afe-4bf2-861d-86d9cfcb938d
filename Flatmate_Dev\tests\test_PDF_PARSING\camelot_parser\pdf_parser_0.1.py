#!/usr/bin/env python3
"""
PDF Parser v0.1 - Stage 1 of PDF Processing Pipeline

Extracts tables from PDF and identifies account boundaries.
Outputs structured JSON optimised for downstream parsing.
"""

import pandas as pd
import camelot
import json
import os
import argparse
from datetime import datetime
import re

def identify_account_header(df):
    """Identify if a table contains account header information."""
    if df.empty:
        return False, {}
    
    account_metadata = {}
    
    # Look for account header patterns
    for idx, row in df.iterrows():
        # Convert all cells to strings and join with spaces
        row_str = ' '.join(str(cell).strip() for cell in row if pd.notna(cell))
        
        # More robust pattern matching
        if 'Account Name:' in row_str or 'Account Name' in row_str:
            # Extract account name more robustly
            if len(row) > 1 and pd.notna(row.iloc[1]) and str(row.iloc[1]).strip():
                account_metadata['account_name'] = str(row.iloc[1]).strip()
            # Try to find account name in next row if not in this one
            elif idx + 1 < len(df) and pd.notna(df.iloc[idx + 1, 0]):
                account_metadata['account_name'] = str(df.iloc[idx + 1, 0]).strip()
        
        elif 'Account Number:' in row_str or 'Account Number' in row_str:
            # Extract account number
            if len(row) > 1 and pd.notna(row.iloc[1]):
                account_number = str(row.iloc[1]).strip()
                # Clean up account number format
                account_number = re.sub(r'\s+', '', account_number)
                account_metadata['account_number'] = account_number
        
        elif 'Product Name:' in row_str or 'Product Name' in row_str:
            # Extract product name
            if len(row) > 1 and pd.notna(row.iloc[1]):
                account_metadata['product_name'] = str(row.iloc[1]).strip()
        
        elif 'Personalised Name:' in row_str or 'Personalised Name' in row_str:
            # Extract personalised name
            if len(row) > 1 and pd.notna(row.iloc[1]):
                account_metadata['personalised_name'] = str(row.iloc[1]).strip()
        
        elif 'Statement Period:' in row_str or 'Statement Period' in row_str:
            # Statement period might be in different columns
            period_text = ' '.join(str(cell).strip() for cell in row[1:] if pd.notna(cell))
            account_metadata['statement_period'] = period_text
    
    # Set default values for missing metadata
    if 'account_name' not in account_metadata and 'account_number' in account_metadata:
        account_metadata['account_name'] = f"Account {account_metadata['account_number']}"
    
    return len(account_metadata) > 0, account_metadata

def identify_transaction_table(df):
    """Identify if a table contains transaction data."""
    if df.empty:
        return False
    
    # Look for transaction header pattern
    header_row = df.iloc[0] if not df.empty else None
    if header_row is not None:
        header_str = ' '.join(str(cell).strip() for cell in header_row if pd.notna(cell))
        
        # More robust pattern matching for transaction headers
        has_date = 'Date' in header_str
        has_transaction = 'Transaction' in header_str or 'Description' in header_str
        has_money_cols = ('Withdrawals' in header_str or 'Debits' in header_str or 
                         'Deposits' in header_str or 'Credits' in header_str or 
                         'Balance' in header_str)
        
        return has_date and has_transaction and has_money_cols
    
    return False

def classify_row_type(row, has_transaction_columns=True):
    """Classify a transaction row as master, continuation, or metadata."""
    if not has_transaction_columns:
        return 'metadata'
    
    # Check if row has monetary values (master transaction)
    withdrawals = str(row.iloc[2]) if len(row) > 2 else ''
    deposits = str(row.iloc[3]) if len(row) > 3 else ''
    balance = str(row.iloc[4]) if len(row) > 4 else ''
    
    # Remove currency symbols and check for numeric values
    withdrawals_clean = re.sub(r'[$,]', '', withdrawals).strip()
    deposits_clean = re.sub(r'[$,]', '', deposits).strip()
    balance_clean = re.sub(r'[$,]', '', balance).strip()
    
    try:
        # If any monetary column has a numeric value, it's a master row
        if (withdrawals_clean and float(withdrawals_clean)) or \
           (deposits_clean and float(deposits_clean)) or \
           (balance_clean and float(balance_clean)):
            return 'master'
    except (ValueError, TypeError):
        pass
    
    # If it has a date but no monetary values, likely continuation or metadata
    date_col = str(row.iloc[0]) if len(row) > 0 else ''
    if date_col.strip():
        return 'continuation'
    else:
        return 'continuation'  # Empty date = continuation of previous transaction

def extract_pdf_tables(pdf_path, output_path):
    """Extract tables from PDF and create structured output."""
    print(f"Extracting tables from {pdf_path}...")
    
    # Extract source file name for metadata
    source_file = os.path.basename(pdf_path)
    
    try:
        tables = camelot.read_pdf(pdf_path, flavor='stream', pages='all')
        print(f"Found {len(tables)} tables")
    except Exception as e:
        print(f"Error reading PDF: {e}")
        return
    
    # Build structured output
    output_data = {
        "extraction_metadata": {
            "pdf_path": pdf_path,
            "source_file": source_file,
            "extraction_timestamp": datetime.now().isoformat(),
            "total_tables": len(tables),
            "camelot_version": camelot.__version__
        },
        "accounts": []
    }
    
    # First pass: identify all account headers
    account_headers = []
    for i, table in enumerate(tables):
        table_num = i + 1
        df = table.df
        
        is_account_header, account_metadata = identify_account_header(df)
        if is_account_header:
            account_metadata['table_number'] = table_num
            account_headers.append(account_metadata)
            print(f"Found account header in table {table_num}: {account_metadata.get('account_number', 'Unknown')}")
    
    # Remove duplicate account headers (same account number)
    unique_accounts = {}
    for header in account_headers:
        account_number = header.get('account_number', '')
        if account_number:
            if account_number not in unique_accounts or len(header) > len(unique_accounts[account_number]):
                unique_accounts[account_number] = header
    
    # Initialize account entries
    for account_number, metadata in unique_accounts.items():
        account_entry = {
            "account_metadata": metadata,
            "raw_transactions": []
        }
        output_data["accounts"].append(account_entry)
    
    # Second pass: process transaction tables and associate with accounts
    current_account_idx = 0
    
    for i, table in enumerate(tables):
        table_num = i + 1
        df = table.df
        
        print(f"Processing table {table_num}...")
        
        # Check if this table contains account header info
        is_account_header, account_metadata = identify_account_header(df)
        
        if is_account_header:
            # Find matching account in our unique accounts
            account_number = account_metadata.get('account_number', '')
            if account_number:
                for idx, account in enumerate(output_data["accounts"]):
                    if account["account_metadata"].get('account_number', '') == account_number:
                        current_account_idx = idx
                        break
        
        # Check if this table contains transaction data
        elif identify_transaction_table(df):
            print(f"  Found transaction table, associating with account {current_account_idx}")
            
            if not output_data["accounts"]:
                # No account metadata found yet, create placeholder
                account_entry = {
                    "account_metadata": {
                        "account_number": f"UNKNOWN_TABLE_{table_num}",
                        "account_name": "Unknown Account"
                    },
                    "raw_transactions": []
                }
                output_data["accounts"].append(account_entry)
                current_account_idx = 0
            
            # Process transaction rows (skip header)
            current_account = output_data["accounts"][current_account_idx]
            
            for row_idx, row in df.iloc[1:].iterrows():  # Skip header row
                row_type = classify_row_type(row)
                
                transaction_row = {
                    "table_number": table_num,
                    "row_index": row_idx,
                    "row_data": [str(cell).strip() if pd.notna(cell) else '' for cell in row],
                    "row_type": row_type
                }
                
                current_account["raw_transactions"].append(transaction_row)
        
        else:
            print(f"  Skipping table {table_num} (no recognisable pattern)")
    
    # Save structured output
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    with open(output_path, 'w') as f:
        json.dump(output_data, f, indent=2)
    
    print(f"Structured data saved to {output_path}")
    print(f"Found {len(output_data['accounts'])} unique accounts")
    
    # Generate account summary
    print("\nAccount Summary:")
    for i, account in enumerate(output_data['accounts']):
        metadata = account['account_metadata']
        tx_count = len(account['raw_transactions'])
        print(f"  Account {i+1}: {metadata.get('account_number', 'Unknown')}")
        print(f"    Name: {metadata.get('account_name', 'Unknown')}")
        print(f"    Transactions: {tx_count}")
    print("\nDone processing PDF.")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description='Extract and structure tables from PDF (Stage 1 of pipeline)'
    )
    parser.add_argument(
        'pdf_file',
        type=str,
        help='Path to input PDF file'
    )
    parser.add_argument(
        'output_file',
        type=str,
        help='Path for structured JSON output'
    )
    
    args = parser.parse_args()
    extract_pdf_tables(args.pdf_file, args.output_file)
