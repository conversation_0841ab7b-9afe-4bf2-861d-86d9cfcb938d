#!/usr/bin/env python3
"""
Test script to verify the UpdateDataViewManager fixes and auto-import polling.
"""

import os
import sys
from pathlib import Path

# Add the flatmate source directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'flatmate', 'src'))

def test_view_manager_import():
    """Test that the view manager can be imported and instantiated."""
    print("Testing UpdateDataViewManager import...")
    
    try:
        from fm.modules.update_data.view_context_manager import UpdateDataViewManager
        
        # Create instance
        view_manager = UpdateDataViewManager()
        print("✅ PASS: UpdateDataViewManager imported and instantiated successfully")
        
        # Test method exists
        if hasattr(view_manager, 'configure_view_for_workflow'):
            print("✅ PASS: configure_view_for_workflow method exists")
        else:
            print("❌ FAIL: configure_view_for_workflow method missing")
            return False
            
        if hasattr(view_manager, 'get_auto_import_status'):
            print("✅ PASS: get_auto_import_status method exists")
        else:
            print("❌ FAIL: get_auto_import_status method missing")
            return False
            
        if hasattr(view_manager, 'should_open_update_data_on_startup'):
            print("✅ PASS: should_open_update_data_on_startup method exists")
        else:
            print("❌ FAIL: should_open_update_data_on_startup method missing")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_auto_import_status():
    """Test the auto-import status checking functionality."""
    print("Testing auto-import status functionality...")
    
    try:
        from fm.modules.update_data.view_context_manager import UpdateDataViewManager
        
        view_manager = UpdateDataViewManager()
        
        # Test getting auto-import status
        status = view_manager.get_auto_import_status()
        
        print(f"Auto-import status: {status}")
        
        # Check status structure
        required_keys = ['enabled', 'path', 'pending_files', 'last_check']
        for key in required_keys:
            if key in status:
                print(f"✅ PASS: Status contains '{key}' key")
            else:
                print(f"❌ FAIL: Status missing '{key}' key")
                return False
        
        # Test startup decision
        should_open = view_manager.should_open_update_data_on_startup()
        print(f"Should open Update Data on startup: {should_open}")
        
        return True
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_file_scanning():
    """Test the file scanning functionality with a temporary folder."""
    print("Testing file scanning functionality...")
    
    try:
        from fm.modules.update_data.view_context_manager import UpdateDataViewManager
        import tempfile
        
        view_manager = UpdateDataViewManager()
        
        # Create a temporary directory with some CSV files
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # Create some test CSV files
            (temp_path / "test1.csv").write_text("col1,col2\nval1,val2")
            (temp_path / "test2.csv").write_text("col1,col2\nval3,val4")
            (temp_path / "not_csv.txt").write_text("This is not a CSV")
            
            # Test scanning
            pending_files = view_manager._scan_for_pending_files(str(temp_path))
            
            print(f"Found {len(pending_files)} pending files")
            print(f"Files: {pending_files}")
            
            # Should find 2 CSV files
            if len(pending_files) == 2:
                print("✅ PASS: Found correct number of CSV files")
            else:
                print(f"❌ FAIL: Expected 2 CSV files, found {len(pending_files)}")
                return False
                
            # Check that only CSV files are included
            csv_files = [f for f in pending_files if f.endswith('.csv')]
            if len(csv_files) == len(pending_files):
                print("✅ PASS: Only CSV files included")
            else:
                print("❌ FAIL: Non-CSV files included")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_presenter_integration():
    """Test that the presenter can use the view manager."""
    print("Testing presenter integration...")
    
    try:
        # Test that the import works
        from fm.modules.update_data.ud_presenter import UpdateDataPresenter
        
        print("✅ PASS: UpdateDataPresenter imports successfully with view_manager")
        
        # Check that the view manager is imported
        import fm.modules.update_data.ud_presenter as presenter_module
        if hasattr(presenter_module, 'UpdateDataViewManager'):
            print("✅ PASS: UpdateDataViewManager is imported in presenter module")
        else:
            print("❌ FAIL: UpdateDataViewManager not imported in presenter module")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    print("=== UpdateDataViewManager Fixes Verification ===")
    print()
    
    tests = [
        ("View Manager Import", test_view_manager_import),
        ("Auto-Import Status", test_auto_import_status),
        ("File Scanning", test_file_scanning),
        ("Presenter Integration", test_presenter_integration),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"Running: {test_name}")
        result = test_func()
        results.append((test_name, result))
        print()
    
    print("=== Test Results ===")
    passed = 0
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nPassed: {passed}/{len(tests)}")
    
    if passed == len(tests):
        print("🎉 All view manager fixes verified!")
        print("\nNext steps:")
        print("1. Test the morphic UI behavior (database vs file-only mode)")
        print("2. Test auto-import folder polling on app startup")
        print("3. Implement the integrated auto-import configuration UI")
    else:
        print("⚠️  Some issues remain - check failed tests above")
