# pdfplumber PDF Parser Research

## Overview
pdfplumber is a Python library for extracting information from PDF files. It's built on pdfminer.six and provides a more user-friendly interface for extracting text, tables, and other elements from PDFs. It's particularly useful for extracting text with precise positioning information.

## Installation
```bash
pip install pdfplumber
```

Dependencies:
- Python 3.6+
- pdfminer.six
- Pillow
- Wand (optional, for image extraction)

## Core API

### Basic Usage
```python
import pdfplumber

# Open a PDF
with pdfplumber.open('file.pdf') as pdf:
    # Get the first page
    page = pdf.pages[0]
    
    # Extract text
    text = page.extract_text()
    
    # Extract tables
    tables = page.extract_tables()
    
    # Extract images
    images = page.images
```

### Key Methods and Properties

#### `pdfplumber.open()`
Opens a PDF file and returns a `PDF` object.

**Parameters:**
- `path_or_fp`: Path to the PDF file or a file-like object
- `password`: Password for encrypted PDFs
- `use_cropbox`: Use the crop box instead of the media box for page dimensions

#### `PDF` Object
Represents the entire PDF document.

**Properties:**
- `pdf.pages`: List of all pages
- `pdf.metadata`: PDF metadata
- `pdf.doc`: The underlying pdfminer document

**Methods:**
- `pdf.close()`: Close the PDF file

#### `Page` Object
Represents a single page in the PDF.

**Properties:**
- `page.width`, `page.height`: Page dimensions
- `page.page_number`: Page number (0-indexed)
- `page.chars`: List of all characters with position data
- `page.lines`: List of all lines with position data
- `page.rects`: List of all rectangles
- `page.curves`: List of all curves
- `page.images`: List of all images

**Methods:**
- `page.extract_text()`: Extract all text from the page
- `page.extract_words()`: Extract words with position data
- `page.extract_tables()`: Extract tables from the page
- `page.crop(bbox)`: Create a cropped version of the page
- `page.filter(predicate)`: Filter page elements based on a predicate function
- `page.to_image()`: Convert page to a PIL image (requires Pillow)

### Table Extraction
Tables are extracted as a list of lists, where each inner list represents a row:

```python
tables = page.extract_tables()
for table in tables:
    for row in table:
        print(row)  # Each row is a list of cell values
```

## Output Structure

### Text Extraction
`page.extract_text()` returns a string with all text on the page, preserving line breaks.

### Word Extraction
`page.extract_words()` returns a list of dictionaries, each representing a word:

```python
[
    {
        'text': 'Hello',
        'x0': 100.0,
        'x1': 150.0,
        'top': 50.0,
        'bottom': 65.0,
        'upright': True,
        'direction': 1
    },
    # More words...
]
```

### Table Extraction
`page.extract_tables()` returns a list of tables, where each table is a list of rows, and each row is a list of cell values:

```python
[
    [
        ['Header1', 'Header2', 'Header3'],
        ['Row1Col1', 'Row1Col2', 'Row1Col3'],
        ['Row2Col1', 'Row2Col2', 'Row2Col3']
    ],
    # More tables...
]
```

### Character Extraction
`page.chars` returns a list of dictionaries, each representing a character:

```python
[
    {
        'text': 'H',
        'x0': 100.0,
        'x1': 110.0,
        'top': 50.0,
        'bottom': 65.0,
        'upright': True,
        'fontname': 'Arial',
        'size': 12.0
    },
    # More characters...
]
```

## Advanced Features

### Table Settings
Customize table extraction:

```python
tables = page.extract_tables({
    'vertical_strategy': 'lines',
    'horizontal_strategy': 'text',
    'explicit_vertical_lines': [100, 200, 300],
    'explicit_horizontal_lines': [50, 100, 150],
    'snap_tolerance': 3,
    'join_tolerance': 3,
    'edge_min_length': 3,
    'min_words_vertical': 3,
    'min_words_horizontal': 1
})
```

### Working with Specific Areas
Extract text from a specific area:

```python
# Crop to a specific area (x0, top, x1, bottom)
crop = page.crop((100, 100, 400, 300))
text = crop.extract_text()
```

### Converting to Images
Convert pages to images for visual debugging:

```python
img = page.to_image()
img.draw_rects(page.extract_words())
img.save("page_with_words.png")
```

## Strengths and Weaknesses

### Strengths
- Precise character and word positioning
- Flexible table extraction strategies
- Good text extraction with formatting preservation
- Visual debugging tools
- Simple and intuitive API
- No external dependencies (except optional ones)

### Weaknesses
- Table extraction can be unreliable for complex tables
- Slower than some alternatives for large documents
- No built-in support for OCR
- Limited support for form fields
- Table extraction requires manual tuning for best results

## Integration with Flatmate

For integrating pdfplumber with the Flatmate project:

```python
from fm.core.services.logger import log
import pandas as pd

def extract_text_from_pdf(pdf_path):
    try:
        log.info(f"Extracting text from {pdf_path} using pdfplumber")
        with pdfplumber.open(pdf_path) as pdf:
            text = ""
            for page in pdf.pages:
                text += page.extract_text() + "\n\n"
            log.info(f"Extracted {len(text)} characters of text")
            return text
    except Exception as e:
        log.error(f"Error extracting text: {str(e)}")
        return ""

def extract_tables_from_pdf(pdf_path):
    try:
        log.info(f"Extracting tables from {pdf_path} using pdfplumber")
        tables = []
        with pdfplumber.open(pdf_path) as pdf:
            for i, page in enumerate(pdf.pages):
                page_tables = page.extract_tables()
                if page_tables:
                    log.info(f"Found {len(page_tables)} tables on page {i+1}")
                    for j, table_data in enumerate(page_tables):
                        # Convert to DataFrame
                        df = pd.DataFrame(table_data)
                        # Use first row as header if it looks like a header
                        if not df.empty:
                            df.columns = df.iloc[0]
                            df = df[1:]
                        tables.append(df)
            
            log.info(f"Extracted {len(tables)} tables in total")
            return tables
    except Exception as e:
        log.error(f"Error extracting tables: {str(e)}")
        return []
```

## Resources
- [Official Documentation](https://github.com/jsvine/pdfplumber)
- [GitHub Repository](https://github.com/jsvine/pdfplumber)
- [PyPI Package](https://pypi.org/project/pdfplumber/)
